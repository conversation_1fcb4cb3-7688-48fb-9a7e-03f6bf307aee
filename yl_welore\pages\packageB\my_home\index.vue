<template>
  <view>
    <view class="triangle-downleft">
      <cu-custom :isSearch="false" :isBack="true" :ShowUid="false">
        <view slot="backText">返回</view>
        <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">个人主页
        </view>
      </cu-custom>

      <view class="flex align-center">
        <view class="padding-sm margin-xs">
          <view @tap="previewImage" :data-src="user_info.user_head_sculpture"
            class="cu-avatar round home margin-left user-avatar-enhanced" :style="'background-image:url(' + user_info.user_head_sculpture + ');'
              ">
            <view style="z-index: 100" :class="'cu-tag badge ' +
              (user_info.gender == 2
                ? 'cuIcon-female bg-pink'
                : 'cuIcon-male bg-blue')
              ">
            </view>
            <image class="now_level" style="
                height: 145rpx;
                width: 145rpx;
                position: absolute;
                max-width: initial;
              " :src="user_info.avatar_frame"></image>
          </view>
        </view>
        <view class="padding-sm" style="flex-shrink: 0">
          <view v-if="uid == id" @tap="open_url('/yl_welore/pages/packageD/user_medal/index')"
            class="cu-tag round personalize-btn" style="
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: #fff;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);height: 60rpx;
            ">
            ✨ 个性装扮
          </view>
        </view>
      </view>

      <view class="flex flex-wrap" style="position: absolute; width: 100%; top: 20%">
        <view class="basis-xl margin-xs padding-sm">
          <view class="user-name-section" style="font-weight: 700; font-size: 18px">
            <text style="vertical-align: middle" :class="user_info.special">{{ user_info.user_nick_name }}</text>
            <image v-if="user_info.is_vip == 1" :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'
              " style="
                width: 20px;
                height: 20px;
                vertical-align: middle;
                margin-left: 5px;
              "></image>
            <image v-if="uid != id" mode="widthFix" class="now_level" :src="user_info.level_info.level_icon"
              style="vertical-align: middle; width: 45rpx; margin-left: 5px"></image>
            <image mode="heightFix" class="now_level" v-if="user_info.merit" :src="user_info.merit.merit_icon"
              style="height: 13px; vertical-align: middle; margin-left: 5px"></image>
          </view>
          <view style="margin-top: 10px; font-size: 11px">
            <block v-for="(item, index) in user_info.attest.info" :key="index">
              <view style="margin: 3px 0px">
                <image :src="item.at_icon" style="width: 30rpx; height: 30rpx; vertical-align: middle">
                </image>
                <text style="vertical-align: middle; margin-left: 5px">{{ item.at_name }}（{{ item.ut_inject }}）
                </text>
              </view>
            </block>
          </view>
          <view @tap="get_autograph" class="text_num" style="color: #999; font-size: 14px; margin: 10px 0px">
            {{
              user_info.autograph == ""
                ? "什么也没有留下..."
                : user_info.autograph
            }}
          </view>
        </view>
      </view>

      <view style="position: absolute; top: 13%; right: 5%">
        <view @tap="cancel" style="text-align: center" v-if="user_info.is_user == 0 && user_info.id != uid">
          <view class="follow-btn" style="
              font-size: 12px;
              padding: 9px 12px;
              border-radius: 30px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: #fff;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
              border: none;
            ">
            <text style="font-size: 14px">➕</text>
            <text style="font-weight: 500; margin-left: 5px">关注</text>
          </view>
        </view>
        <view @tap="cancel" style="text-align: center" v-if="user_info.is_user == 1 && user_info.id != uid">
          <view class="followed-btn" style="
              color: #666666;
              font-size: 12px;
              padding: 9px 12px;
              border-radius: 30px;
              border: 1px solid #666666;
              background: #f5f5f5;
            ">
            <text style="font-size: 14px">✅</text>
            <text style="font-weight: 500; margin-left: 5px">已关注</text>
          </view>
        </view>
      </view>
      <!-- 用户操作工具栏 -->
      <view class="user-actions-toolbar">
        <!-- 守护榜 -->
        <view v-if="copyright.guard_arbor == 1" class="action-item" @tap="guard">
          <view class="action-icon-container">
            <image class="action-icon" :src="http_root + 'addons/yl_welore/web/static/applet_icon/bill_bg.png'"></image>
          </view>
          <view class="action-label">守护榜</view>
        </view>

        <!-- 拉黑 -->
        <view v-if="id != uid" class="action-item action-warning" @tap="lahei">
          <view class="action-icon-container">
            <image class="action-icon" :src="http_root + 'addons/yl_welore/web/static/applet_icon/heihei.png'"></image>
          </view>
          <view class="action-label">拉黑</view>
        </view>

        <!-- 举报 -->
        <view v-if="id != uid" class="action-item action-danger" @tap="jubao_do">
          <view class="action-icon-container">
            <image class="action-icon" :src="http_root + 'addons/yl_welore/web/static/applet_icon/jubao_b.png'"></image>
          </view>
          <view class="action-label">举报</view>
        </view>

        <!-- 留言 -->
        <view v-if="id != uid && copyright.speech_arbor == 1 && user_info.is_phone == 1 &&copyright.version==0"
          class="action-item action-primary" @tap="s_url">
          <view class="action-icon-container">
            <image class="action-icon" src="/static/yl_welore/style/icon/liuyan.png"></image>
          </view>
          <view class="action-label">留言</view>
        </view>
      </view>

      <navigator v-if="id == uid && copyright.user_info_update_arbor == 1"
        url="/yl_welore/pages/packageB/edit_user_info/index" hover-class="none"
        style="position: absolute; right: 5%; top: 13%">
        <view class="settings-btn" style="
            font-size: 12px;
            padding: 9px 12px;
            border-radius: 30px;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            box-shadow: 0 4px 15px rgba(252, 182, 159, 0.4);
            border: none;
            float: right;
          ">
          <text style="font-size: 14px">⚙️</text>
          <text style="font-weight: 500; margin-left: 5px">设置</text>
        </view>
      </navigator>
    </view>

    <image v-if="id != uid && version == 0 && copyright.tribute_arbor == 1" @tap="get_liwu" :src="http_root +
      'addons/yl_welore/web/static/applet_icon/ic_liveroom_gift_list.png'
      " style="
        position: fixed;
        width: 52px;
        height: 52px;
        bottom: 18%;
        right: 10px;
        z-index: 100;
      "></image>

    <view class="level-info-container" style="width: 40%; position: absolute; right: 0; top: 29%">
      <view v-if="uid == id && user_info.level_info" class="level-card">
        <!-- 等级标题区域 -->
        <view class="level-header">
          <image mode="widthFix" class="now_level level-icon" :src="user_info.level_info.level_icon"
            style="width: 40rpx; vertical-align: middle;margin-right: 10rpx;"></image>
          <text class="level-name">{{ user_info.level_info.level_name }}</text>
        </view>

        <!-- 经验进度区域 -->
        <view class="experience-section">
          <view class="progress-container">
            <text class="progress-emoji">⚡</text>
            <progress color="#667eea" style="width: 70px; border-radius: 20px;"
              :percent="user_info.level_info.percentage" border-radius="20" stroke-width="3" />
          </view>
          <view class="experience-text">
            <text class="exp-emoji">💎</text>
            <text class="exp-current">{{ user_info.experience }}</text>
            <text class="exp-separator">/</text>
            <text class="exp-next">{{ user_info.level_info.next_level ? user_info.level_info.next_level : "Max"
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="stats-container">
      <view class="stat-item" @click="open_url('/yl_welore/pages/packageB/user_trailing/index?id=' + id)">
        <view class="stat-label">{{ design.landgrave }}</view>
        <view class="stat-number">
          {{ user_info.trailing }}
        </view>
      </view>
      <view class="stat-item" @tap="open_user_follow" :data-id="id" data-key="1">
        <view class="stat-label">关注</view>
        <view class="stat-number">
          {{ user_info.user_track }}
        </view>
      </view>
      <view class="stat-item" @tap="open_user_follow" :data-id="id" data-key="2">
        <view class="stat-label">粉丝</view>
        <view class="stat-number">{{ user_info.user_fs }}
        </view>
      </view>
    </view>

    <view class="nav-enhanced">
      <view class="nav-tab" :class="'cu-item ' + (current == 'tab1' ? 'active-tab' : '')" @tap="handleChange"
        data-key="tab1">
        <text :class="current == 'tab1' ? '_this' : ''" style="font-weight: 700">动态</text>
      </view>
      <view v-if="id == uid" class="nav-tab" :class="'cu-item ' + (current == 'tab2' ? 'active-tab' : '')"
        @tap="handleChange" data-key="tab2">
        <text :class="current == 'tab2' ? '_this' : ''" style="font-weight: 700">待审核</text>
      </view>
    </view>

    <view class="dynamic-card" style="
        background: linear-gradient(to right, #f8f9fa 0%, #e9ecef 100%);
        margin: 10px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
      " v-for="(item, new_index) in new_list" :key="new_index">
      <view>
        <view class="time-display" style="
            margin: 20px;
            float: left;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            color: #333;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          ">
          <view class="font-yl-2" style="
              font-size: 18px;
              width: 37px;
              text-align: center;
              font-weight: 600;
            ">
            {{ item.day }}
          </view>
          <view style="
              font-size: 12px;
              width: 40px;
              text-align: center;
              font-weight: 400;
            ">
            {{ item.month }}
          </view>
          <view class="font-yl-2" style="
              font-size: 12px;
              width: 40px;
              text-align: center;
              font-weight: 400;
            ">
            {{ item.year }}
          </view>
        </view>
        <view :style="(li_index != 0 ? 'padding-left:82px;' : '') +
          'border-bottom:1px solid #F6F8F8;background-color: #fff;'
          " v-for="(li, li_index) in item.list" :key="li_index">
          <view style="margin: 40rpx 5px; float: left; width: 240px">
            <view class="cu-capsule radius">
              <view class="cu-tag bg-blue">
                <text class="cuIcon-discover"></text>
              </view>
              <view class="cu-tag line-grey">
                {{ li.realm_name }}
              </view>
            </view>
            <view v-if="li.gambit_id" @tap="gambit_list" :data-id="li.gambit_id" style="
                font-weight: 300;
                display: inline-block;
                background-color: #ededed;
                border-radius: 20px;
                padding: 2px 10px 2px 2px;
                font-size: 12px;
                margin-bottom: 10px;
                margin-left: 10rpx;
              ">
              <image style="width: 15px; height: 15px; vertical-align: middle" :src="http_root +
                'addons/yl_welore/web/static/mineIcon/material/index_topic.png'
                "></image>
              <text style="
                  vertical-align: middle;
                  margin-left: 5px;
                  letter-spacing: 1px;
                ">{{ li.gambit_name }}
              </text>
            </view>
            <view @tap="get_my_url" :data-id="li.id" :data-type="li.study_type" class="text_num"
              style="font-size: 15px; margin: 8px 0px">
              <rich-text :nodes="li.study_title == '' ? li.study_content : li.study_title
                "></rich-text>
            </view>
            <block v-if="
              li.study_type == 0 ||
              li.study_type == 3 ||
              li.study_type == 4 ||
              li.study_type == 5
            ">
              <view v-if="li.study_type == 4 || li.study_type == 5" class="shadow-warp"
                style="margin-top: 15px; background-color: #f8f8f8">
                <view style="padding: 15px; text-align: center">
                  <view @tap="get_my_url" :data-id="li.id" :data-type="li.study_type" class="text_num"
                    style="font-size: 15px; font-weight: 600">
                    <text v-if="li.study_type == 4">（单选）</text>
                    <text v-if="li.study_type == 5">（多选）</text>
                    <rich-text v-if="li.study_title != ''" :nodes="li.study_title"></rich-text>
                  </view>
                  <view style="height: 10px"></view>
                  <view style="position: relative" v-if="vo_index < 3" v-for="(vo_item, vo_index) in li.vo"
                    :key="vo_index">
                    <view style="
                        width: 95%;
                        height: 40px;
                        border-radius: 5px;
                        line-height: 40px;
                        margin: 5px auto;
                      " class="text_num bg-white" @tap="get_my_url" :data-id="li.id" :data-type="li.study_type">
                      <view class="text-cut" style="
                          z-index: 3;
                          position: relative;
                          width: 70%;
                          margin: 0 auto;
                        ">
                        {{ vo_item.ballot_name }}
                      </view>
                    </view>
                  </view>

                  <view @tap="get_my_url" :data-id="li.id" :data-type="li.study_type" v-if="li.vo.length > 3" style="
                      width: 95%;
                      height: 40px;
                      border-radius: 5px;
                      line-height: 40px;
                      margin: 5px auto;
                    " class="text_num bg-white">查看详情
                    <text class="cuIcon-right lg text-gray"></text>
                  </view>
                </view>
                <view class="flex align-end" style="padding-bottom: 10px">
                  <view class="flex-sub">
                    <view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ li.vo_count }}
                    </view>
                  </view>
                  <view class="flex-sub"></view>
                </view>
              </view>

              <view v-if="li.image_part" @tap="get_my_url" :data-id="li.id" :data-type="li.study_type"
                style="overflow: hidden">
                <block v-for="(img, img_index) in li.image_part" :key="img_index">
                  <view v-if="li.image_part.length == 1" style="width: 100%; text-align: center; padding-left: 5px">
                    <image :lazy-load="true" @tap="previewImage" :src="img"
                      style="width: 100%; height: 135px; border-radius: 3px" mode="aspectFill">
                    </image>
                  </view>

                  <view v-if="li.image_part.length >= 2" style="
                      width: 50%;
                      text-align: center;
                      padding-left: 5px;
                      float: left;
                    ">
                    <image v-if="img_index == 0" :lazy-load="true" @tap="previewImage" :src="img"
                      style="width: 100%; height: 105px; border-radius: 3px" mode="aspectFill">
                    </image>
                    <image v-if="img_index == 1" :lazy-load="true" @tap="previewImage" :src="img"
                      style="width: 100%; height: 105px; border-radius: 3px" mode="aspectFill">
                    </image>
                  </view>
                </block>
              </view>
            </block>
            <view v-if="li.study_type == 1">
              <view>
                <view class="audiosBox">
                  <view class="audioOpen" @tap="play" v-if="!li.is_voice" :data-vo="new_index" :data-key="li_index">
                    <text style="color: #4c9dee; font-size: 25px" class="cicon-play-arrow"></text>
                  </view>
                  <view class="audioOpen" @tap="stop" v-if="li.is_voice" :data-vo="new_index" :data-key="li_index">
                    <text style="color: #4c9dee; font-size: 26px" class="cicon-pause"></text>
                  </view>
                  <view class="slid">
                    <slider @change="sliderChange" :data-key="li_index" :data-vo="new_index" block-size="12px" step="1"
                      :value="li.offset" :max="li.max" selected-color="#4c9dee" />
                    <view @tap="get_my_url" :data-id="li.id" :data-type="li.study_type">
                      <text class="times">{{ li.starttime }}</text>

                      <text class="times">{{ li.study_voice_time }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view @tap="get_my_url" :data-key="new_index" :data-index="li_index" :data-id="li.id"
              :data-type="li.study_type" v-if="li.study_type == 2">
              <view>
                <view v-if="li.image_part.length > 0" class="grid flex-sub col-1" style="position: relative">
                  <image :src="li.image_part[0]" mode="aspectFill"
                    style="height: 135px; margin: 0 auto; border-radius: 5px">
                  </image>
                  <text class="cuIcon-videofill lg text-white" style="
                      font-size: 40px;
                      position: absolute;
                      text-align: center;
                      left: 44%;
                      bottom: 37%;
                      z-index: 1;
                    "></text>
                </view>
                <view v-if="
                  li.image_part.length == null || li.image_part.length == 0
                " class="bg-black padding radius text-center shadow-blur" style="
                    position: relative;
                    margin: 0 auto;
                    height: 135px;
                    z-index: 100;
                    overflow: hidden;
                    border-radius: 5px;
                    font-size: 16px;
                  ">
                  <text class="cuIcon-videofill lg text-white" style="
                      font-size: 40px;
                      position: absolute;
                      text-align: center;
                      left: 44%;
                      bottom: 37%;
                      z-index: 1;
                    "></text>
                </view>
              </view>
            </view>
            <view class="interaction-buttons" :class="'grid col-' +
              (ad.paper_browse_num_hide == 0 ? 3 : 2) +
              ' margin-top text-center'
              ">
              <view class="interaction-btn" v-if="ad.paper_browse_num_hide == 0">
                <button hover-class="none" class="btn-enhanced">
                  <text style="font-size: 16px; vertical-align: middle">👀
                  </text>
                  <text class="index_nav_name" style="
                      color: #666;
                      font-size: 13px;
                      margin-left: 8rpx;
                      vertical-align: middle;
                    ">{{ li.study_heat }}
                  </text>
                </button>
              </view>
              <view class="interaction-btn">
                <button hover-class="none" class="btn-enhanced">
                  <text style="font-size: 16px; vertical-align: middle">👍
                  </text>
                  <text class="index_nav_name" style="
                      color: #666;
                      font-size: 13px;
                      margin-left: 8rpx;
                      vertical-align: middle;
                    ">{{ li.study_laud }}
                  </text>
                </button>
              </view>
              <view class="interaction-btn">
                <button hover-class="none" class="btn-enhanced" @tap="" :data-id="li.id" :data-key="li_index"
                  :data-wey="new_index">
                  <text style="font-size: 16px; vertical-align: middle">💬
                  </text>
                  <text class="index_nav_name" style="
                      color: #666;
                      font-size: 13px;
                      margin-left: 8rpx;
                      vertical-align: middle;
                    ">{{ li.study_repount }}
                  </text>
                </button>
              </view>
            </view>
          </view>

          <view style="clear: both; height: 0"></view>
        </view>
      </view>
    </view>
    <view :class="'cu-load ' + (!my_di ? 'loading' : 'over')"></view>

    <!-- 礼物弹窗 -->
    <view :class="'cu-modal bottom-modal enhanced-modal ' + (liwu ? 'show' : '')">
      <view class="cu-dialog enhanced-dialog">
        <view class="gift-modal-header">
          <view class="modal-title">
            <text class="title-emoji">🎁</text>
            <text class="title-text">选择礼物</text>
          </view>
        </view>

        <view class="gift-modal-body" id="liwu" :animation="animationDataLi">
          <scroll-view :scroll-x="true" class="gift-scroll">
            <view class="gift-list">
              <view :class="'gift-item ' + (li_index == dataListindex ? 'selected' : '')" :data-k="dataListindex"
                :data-id="item.id" @tap="liwu_index" v-for="(item, dataListindex) in li_list" :key="dataListindex">
                <view class="gift-icon-container">
                  <image class="gift-icon" :src="item.tr_icon"></image>
                  <text v-if="li_index == dataListindex" class="selected-emoji">✨</text>
                </view>
                <view class="gift-name">{{ item.tr_name }}</view>
                <view class="gift-price">
                  <text class="price-emoji">💰</text>
                  <text>{{ item.tr_conch }}{{ $state.diy.currency }}</text>
                </view>
              </view>
            </view>
          </scroll-view>

          <view class="balance-section">
            <navigator url="/yl_welore/pages/packageC/user_details/index" hover-class="none">
              <view class="balance-info">
                <text class="balance-label">我的{{ $state.diy.currency }}</text>
                <image class="currency-icon" :src="$state.diy.currency_icon"></image>
                <text class="balance-amount">{{ user_liwu.conch }}</text>
              </view>
            </navigator>
          </view>
        </view>

        <view class="gift-modal-footer">
          <button class="enhanced-btn primary-btn reward-btn" @tap="reward">
            <text class="btn-emoji">🎉</text>
            <text>立即打赏</text>
          </button>
          <button class="enhanced-btn cancel-btn gift-close-btn" @tap="colse_li">
            <text class="btn-emoji">❌</text>
            <text>关闭</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 订阅帖子弹窗 -->
    <view :class="'cu-modal enhanced-modal ' + (purchase_paper_mod && buy_price_type == 0 ? 'show' : '')">
      <view class="cu-dialog enhanced-dialog">
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-emoji">💰</text>
            <text class="title-text">订阅帖子</text>
          </view>
          <view class="close-btn" @tap="hideModal">
            <text class="close-icon">✕</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="price-section">
            <view class="price-label">
              <text class="label-emoji">💎</text>
              <text>{{ $state.diy.currency }}兑换</text>
            </view>
            <view class="price-amount">{{ money }}</view>
          </view>

          <navigator url="/yl_welore/pages/packageC/user_details/index" hover-class="none">
            <view class="balance-info">
              <image class="currency-icon" :src="$state.diy.currency_icon"></image>
              <text class="balance-text">我的剩余{{ $state.diy.currency }}：</text>
              <text class="balance-amount">{{ user_info.is_user_info.conch }}</text>
            </view>
          </navigator>
        </view>

        <view class="modal-footer">
          <button class="enhanced-btn cancel-btn" @tap="hideModal">
            <text class="btn-emoji">❌</text>
            <text>取消</text>
          </button>
          <button class="enhanced-btn confirm-btn primary-btn" @tap="do_paper_money">
            <text class="btn-emoji">💳</text>
            <text>确认支付</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 举报用户弹窗 -->
    <view :class="'cu-modal enhanced-modal ' + (jubao ? 'show' : '')">
      <view class="cu-dialog enhanced-dialog">
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-emoji">⚠️</text>
            <text class="title-text">举报用户</text>
          </view>
          <view class="close-btn" @tap="no_jubao">
            <text class="close-icon">✕</text>
          </view>
        </view>

        <view v-if="jubao" class="modal-body">
          <view class="input-section">
            <view class="input-label">
              <text class="label-emoji">📝</text>
              <text>请详细描述问题</text>
            </view>
            <textarea class="enhanced-textarea" :fixed="true" @input="get_jubao_text" value="" maxlength="300"
              placeholder="请具体说明问题，我们将尽快处理..." />
            <view class="char-count">{{ jubao_text.length }}/300</view>
          </view>
        </view>

        <view class="modal-footer">
          <button class="enhanced-btn cancel-btn" @tap="no_jubao">
            <text class="btn-emoji">❌</text>
            <text>取消</text>
          </button>
          <button class="enhanced-btn confirm-btn danger-btn" @tap="jubao_submit">
            <text class="btn-emoji">🚨</text>
            <text>提交举报</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 个人简介弹窗 -->
    <view :class="'cu-modal enhanced-modal ' + (modal_jianjie == true ? 'show' : '')">
      <view class="cu-dialog enhanced-dialog">
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-emoji">📝</text>
            <text class="title-text">个人简介</text>
          </view>
          <view class="close-btn" @tap="hideModal">
            <text class="close-icon">✕</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="bio-content">
            <text class="bio-text">{{ user_info.autograph || '这个人很懒，什么都没有留下...' }}</text>
          </view>
        </view>
      </view>
    </view>

    <view v-if="!show_my_home" class="container">
      <text @tap="BackPage" class="cicon-back"
        style="position: absolute; top: 110rpx; font-size: 40rpx; left: 20rpx"></text>
      <view class="user_title">{{ user_info.user_nick_name }}</view>
      <view class="home_content">
        <view class="hd" style="transform: rotateZ(deg)">
          <image class="logo" :src="user_info.user_head_sculpture"></image>
          <image class="wave" src="/static/yl_welore/style/icon/wave.png" mode="aspectFill"></image>
          <image class="wave wave-bg" src="/static/yl_welore/style/icon/wave.png" mode="aspectFill"></image>
        </view>
        <view class="bd">
          <view class="smalltitle">
            <text class="cicon-lock"></text>
            主页设置了访问权限
          </view>
          <view class="confirm-btn">
            <button @tap="BackPage">返回</button>
          </view>
        </view>
      </view>
    </view>

    <login id="login" @checkPhoen="check_user_login = false;" :check_user_login="check_user_login"></login>
    <phone id="phone" @close_phone_modal="check_phone_show = false;" :check_phone="check_phone_show"></phone>
  </view>
</template>

<script>
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";

var app = getApp();
var http = require("../../../util/http.js");
const innerAudioContext = uni.getBackgroundAudioManager();
export default {
  components: {
    login,
    phone,
  },
  /**
   * 页面的初始数据
   */
  data() {
    return {
      http_root: app.globalData.http_root,
      show: true,
      jubao: false,
      user_liwu: {},
      sex: 1,
      user_info: {},
      current: "tab1",
      index_page: 1,
      pay_index: 1,
      data_list: [],
      new_list: [],
      page: 1,
      pay_di: false,
      my_di: false,
      del_mod: false,
      liwu: false,
      animationDataLi: {},
      li_number: 1,
      li_list: [],
      home_pl_check: false,
      //首页评论框
      pl_id: 0,
      //评论ID
      home_pl_text: "",
      //首页评论内容
      purchase_paper_mod: false,
      //购买窗口
      money: 0,
      money_id: 0,
      money_index: 0,
      one: 0,
      li_index: null,
      version: 1,
      jubao_text: "",
      admin: 0,
      originalHeight: 0,
      originalWidth: 0,
      modal_jianjie: false,
      show_my_home: true,
      check_user_login: false,
      check_phone_show: false,
      design: {},
      uid: 0,
      id: 0,
      copyright: '',
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.id == undefined) {
      var e = app.globalData.getCache("userinfo");
      options.id = e.uid;
    }
    this.id = options.id;
    var copyright = getApp().globalData.store.getState().copyright;
    if (Object.keys(copyright).length === 0) {
      console.log(!this.copyright);
      this.authority();
    } else {
      this.copyright = copyright;
    }
    var over_time = app.globalData.getCache("over_time");
    var dd = uni.getStorageSync('is_diy');
    console.log(dd);
    var this_time = parseInt(+new Date() / 1000);
    if (dd && over_time > this_time) {
      this.design = dd;
      app.globalData.store.setState({
        diy: dd
      });
    } else {
      this.get_diy();
    }
    this.ad = getApp().globalData.store.getState().ad.info;

    //console.log(this.version);
    //this.get_user_info();
    this.get_liwu_all();
    this.get_my_list();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var e = app.globalData.getCache("userinfo");
    this.uid = e.uid;
    this.get_user_info();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    if (this.current == "tab1") {
      this.index_page = this.index_page + 1;
      this.get_my_list();
    }
    if (this.current == "tab2") {
      this.page = this.page + 1;
      this.get_my_pay();
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    this.show = false;
    //设置数据
    if (forward) {
      return {
        title: forward.title,
        path: "/yl_welore/pages/packageB/my_home/index?id=" + this.id,
        imageUrl: forward.reis_img,
      };
    } else {
      return {
        title: "您的好友给您发了一条信息",
        path: "/yl_welore/pages/packageB/my_home/index?id=" + this.id,
      };
    }
  },
  methods: {

    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      // params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: res => {
          console.log(app.globalData);
          this.design = res.data;
          app.globalData.store.setState({
            diy: res.data
          });
          var n = parseInt(+new Date() / 1000) + 129600;
          uni.setStorageSync("is_diy", res.data);
          app.globalData.setCache('over_time', n);
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_authority';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          this.copyright = res.data;
          app.globalData.store.setState({
            copyright: res.data
          });
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    open_url(url) {
      uni.navigateTo({
        url: url,
      });
    },
    open_user_follow(d) {
      var id = d.currentTarget.dataset.id;
      var type = d.currentTarget.dataset.key;
      if (
        this.user_info["is_enable_fans_privacy"] == 1 &&
        type == 1 &&
        this.admin == 0 &&
        this.uid != this.user_info["id"]
      ) {
        uni.showToast({
          title: "主人开启了权限",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      if (
        this.user_info["is_enable_concern_privacy"] == 1 &&
        type == 2 &&
        this.admin == 0 &&
        this.uid != this.user_info["id"]
      ) {
        uni.showToast({
          title: "主人开启了权限",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      console.log(d);
      uni.navigateTo({
        url: "/yl_welore/pages/packageB/user_follow/index?id=" +
          id +
          "&type=" +
          type,
      });
    },
    get_autograph() {
      this.modal_jianjie = true;
    },
    get_my_url(dd) {
      var e = app.globalData.getCache("userinfo");
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var id = dd.currentTarget.dataset.id;
      var type = dd.currentTarget.dataset.type;
      if (type == 2) {
        var douyin = app.globalData.__PlugUnitScreen(
          "5fb4baf1f25fe251685b526dc8c30b8f"
        );
        var info =
          this.new_list[dd.currentTarget.dataset.key].list[
          dd.currentTarget.dataset.index
          ];
        if (
          dd.currentTarget.dataset.type == 2 &&
          info.is_buy == 0 &&
          e.user_phone &&
          douyin
        ) {
          uni.navigateTo({
            url: "/yl_welore/pages/packageF/full_video/index?id=" +
              dd.currentTarget.dataset.id,
          });
          return;
        }
      }
      uni.navigateTo({
        url: "/yl_welore/pages/packageA/article/index?id=" + id + "&type=" + type,
      });
    },
    preventTouchMove() {
    },
    //播放声音
    play(e) {
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var index = e.currentTarget.dataset.key;
      var o_index = e.currentTarget.dataset.vo;
      var nuw = this.new_list;
      console.log(nuw);
      var key = 1;
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        },
      });
      for (var i = 0; i < nuw[o_index].list.length; i++) {
        this.$set(this.new_list[o_index].list[i], "is_voice", false);
      }
      console.log("播放");
      innerAudioContext.src = nuw[o_index].list[index].study_voice;
      innerAudioContext.title = nuw[o_index].list[index]["study_title"] ?
        nuw[o_index].list[index]["study_title"] :
        "暂无标题";
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        var starttime = min + ":" + sec; /*  00:00  */
        this.$set(this.new_list[o_index].list[index], "starttime", starttime);
        this.$set(this.new_list[o_index].list[index], "offset", offset);
      });
      // innerAudioContext.play();
      this.$set(this.new_list[o_index].list[index], "is_voice", true);
      this.new_list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        this.$set(this.new_list[o_index].list[index], "is_voice", false);
        this.starttime = "00:00";
        this.offset = 0;
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log("暂停");
      var index = e.currentTarget.dataset.key;
      var o_index = e.currentTarget.dataset.vo;
      this.$set(this.new_list[o_index].list[index], "is_voice", false);
    },
    // 进度条拖拽
    sliderChange(e) {
      var index = e.currentTarget.dataset.key;
      var o_index = e.currentTarget.dataset.vo;
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      this.$set(this.new_list[o_index].list[index], "is_voice", true);
    },
    /**
     * 举报
     */
    jubao_do(e) {

      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      // 创建一个动画实例
      var animation = uni.createAnimation({
        // 动画持续时间
        duration: 150,
        // 定义动画效果，当前是匀速
        timingFunction: "linear",
      });
      // 将该变量赋值给当前动画
      this.animation = animation;
      // 先在y轴偏移，然后用step()完成一个动画
      animation.translateY(230).step();
      // 用setData改变当前动画
      this.animationJbData = animation.export();
      this.jubao = true;
      // 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
      setTimeout(() => {
        animation.translateY(0).step();
        this.animationJbData = animation.export();
      }, 100);
    },
    /**
     * 记录举报内容
     */
    get_jubao_text(e) {
      var text = e.detail.value;
      this.jubao_text = text;
    },
    /**
     * 关闭举报
     */
    no_jubao() {
      this.jubao = false;
    },
    /**
     * 投诉用户提交
     */
    jubao_submit() {
      if (this.jubao_text == "") {
        uni.showToast({
          title: "内容不能为空！",
          icon: "none",
          duration: 2000,
        });
        return;
      }
      var b = app.globalData.api_root + "User/add_jubao";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.id = this.id;
      params.ment_caption = this.jubao_text;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            this.jubao_text = "";
            this.jubao = false;
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          } else {
            this.jubao_text = "";
            this.jubao = false;
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    s_url() {
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 0) {
        uni.navigateTo({
          url: "/yl_welore/pages/packageB/private_letter/index?id=" + this.id,
        });
      } else {
        uni.showToast({
          title: "游客不能留言",
          icon: "none",
          duration: 2000,
        });
      }
    },
    gambit_list(d) {
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: "/yl_welore/pages/gambit/index?id=" + id,
      });
    },
    //购买付费帖子
    purchase_paper(data) {
      console.log(data);
      var id = data.currentTarget.dataset.id;
      var money = data.currentTarget.dataset.money;
      var index = data.currentTarget.dataset.index;
      var one = data.currentTarget.dataset.one;
      this.buy_price_type = this.new_list[one].list[index].buy_price_type;
      this.money = money;
      this.money_id = id;
      this.purchase_paper_mod = true;
      this.money_index = index;
      this.one = one;
    },
    //do
    do_paper_money() {
      var b = app.globalData.api_root + "User/do_paper_money_new";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.money_id = this.money_id;
      params.money = this.money;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
            var list =
              "new_list[" +
              this.one +
              "].list[" +
              this.money_index +
              "].purchase";
            this.$set(
              this.new_list[this.one].list[this.money_index],
              "purchase",
              "1"
            );
            this.purchase_paper_mod = false;
            this.get_user_info();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    //首页评论
    home_pl(e) {
      console.log(e);
      this.home_pl_check = true;
      this.pl_id = e.currentTarget.dataset.id;
      this.pl_key = e.currentTarget.dataset.key;
      this.pl_wey = e.currentTarget.dataset.wey;
    },
    //评论采集
    home_pl_cai(e) {
      this.home_pl_text = e.detail.value;
    },
    /**
     * 获取礼物列表
     */
    get_liwu_all() {
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      var b = app.globalData.api_root + "User/get_liwu";
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            this.li_list = res.data.info;
            this.user_liwu = res.data.user_info;
          } else {
            this.li_if = true;
            this.li_msg = res.data.msg;
          }
          setTimeout(() => {
            this.li_if = false;
          }, 3000);
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 送礼物
     */
    get_liwu() {
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      this.liwu = true;
    },
    /**
     * 点击送礼
     */
    liwu_index(e) {
      var li_index = e.currentTarget.dataset.k;
      var id = e.currentTarget.dataset.id;
      var li_info = this.li_list[li_index];
      this.li_index = li_index;
      //this.id = id;
      this.li_number = 1;
      this.li_sum = li_info["tr_conch"];
    },
    /**
     * 关闭礼物
     */
    colse_li() {
      this.liwu = false;
    },
    /**
     * 打赏
     */
    reward() {
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.num = this.li_number;
      params.uid = e.uid;
      params.user_id = this.id;
      params.li_id = this.li_list[this.li_index]["id"];
      var b = app.globalData.api_root + "User/user_reward_new";
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
            this.get_user_info();
            this.get_liwu_all();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 送礼数量
     */
    handleChange1({
      detail
    }) {
      var num = detail.value;
      var li_info = this.li_list[this.li_index];
      this.li_number = num;
      this.li_sum = (li_info["tr_conch"] * num).toFixed(2);
    },
    handleChange(detail) {
      var key = detail.currentTarget.dataset.key;
      uni.showLoading({
        title: "加载中...",
        mask: true,
      });
      this.data_list = [];
      this.new_list = [];
      this.page = 1;
      this.index_page = 1;
      this.pay_di = false;
      this.my_di = false;
      if (key == "tab1") {
        this.get_my_list();
      }
      if (key == "tab2") {
        this.get_my_pay();
      }
      this.current = key;
      uni.hideLoading();
    },
    /**
     * 取消关注/关注
     */
    cancel() {
      var b = app.globalData.api_root + "User/get_user_cancel";
      var e = app.globalData.getCache("userinfo");
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = this.id;
      params.this_uid = e.uid;
      params.is_user = this.user_info["is_user"];
      params.type = 1;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
            this.get_user_info();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 守护
     */
    guard() {
      uni.navigateTo({
        url: "/yl_welore/pages/packageB/user_guard/index?id=" + this.id,
      });
    },
    /**
     * 获取会员信息
     */
    get_user_info() {
      var b = app.globalData.api_root + "User/get_user_info_my";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = this.id;
      params.this_uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (
              res.data.info["user_home_access_status"] == 0 &&
              this.id != e.id &&
              res.data.info.admin == 0
            ) {
              this.show_my_home = false;
              this.user_info = res.data.info;
            } else {
              this.show_my_home = true;
              this.user_info = res.data.info;
              this.admin = res.data.info.admin;
              this.sex = res.data.info.gender;
              this.version = res.data.info.version;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 获取首页数据
     */
    get_my_pay() {
      var b = app.globalData.api_root + "User/get_my_list_sh";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.id = this.id;
      params.type = this.current;
      params.index_page = this.page;
      var allMsg = this.new_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0 || res.data.info.length < 3) {
              this.my_di = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            this.new_list = allMsg;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 获取首页数据
     */
    get_my_list() {
      var b = app.globalData.api_root + "User/get_my_list";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.id = this.id;
      params.type = this.current;
      params.index_page = this.index_page;
      var allMsg = this.new_list;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            if (res.data.info.length == 0 || res.data.info.length < 3) {
              this.my_di = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            this.new_list = allMsg;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    /**
     * 删除对话
     */
    del_mod_do(e) {
      var id = e.currentTarget.dataset.id;
      this.paper_id = id;
      this.del_mod = true;
    },
    /**
     * 关闭
     */
    hideModal() {
      this.del_mod = false;
      this.home_pl_check = false;
      this.purchase_paper_mod = false;
      this.modal_jianjie = false;
    },
    lahei() {
      uni.showModal({
        title: "提示",
        content: "确定要拉黑吗？拉黑后将无法收到他的留言！",
        success: (res) => {
          if (res.confirm) {
            this.lahei_do();
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    /**
     * 删除
     */
    lahei_do() {
      var b = app.globalData.api_root + "User/lahei_do";
      var e = app.globalData.getCache("userinfo");
      const params = {};
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: "none",
              duration: 2000,
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: "提示",
            content: "网络繁忙，请稍候重试！",
            showCancel: false,
            success: function (res) {
            },
          });
        },
      });
    },
    BackPage() {
      var pages = getCurrentPages();
      var Page = pages[pages.length - 1]; //当前页
      var prevPage = pages[pages.length - 2]; //上一个页面
      if (pages.length == 1) {
        this.toHome();
        return;
      }
      uni.navigateBack();
    },
    toHome() {
      uni.reLaunch({
        url: "/yl_welore/pages/index/index",
      });
    },
    previewImage(e) {
      var current = e.currentTarget.dataset.src;
      uni.previewImage({
        urls: [current], // 当前显示图片的http链接
      });
    },
  },
};
</script>
<style>
page {
  background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
}

.yes_pos {
  position: relative;
}

.yl_style8:after {
  background: #cff0f9 !important;
}

.nav-capsule {
  display: flex;
  align-items: center;
  width: 100rpx;
  justify-content: space-around;
  border-radius: 50%;
  z-index: 999999999;
}

.text_wt {
  color: #000;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
  overflow: inherit;
}

.text_num {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.liwu_col {
  box-sizing: border-box;
  text-align: center;
  padding-top: 10rpx;
  display: inline-block;
  width: 20%;
  height: 6.8em;
}

.liwu_border {
  border: 2px solid #c9f;
  box-shadow: 0px 0px 10px 0px #c9f;
  border-radius: 10px;
}

.not_pos {
  width: 47% !important;
}

.wx_liwu {
  width: 88%;
  background: #fff;
  height: 78rpx;
  line-height: 72rpx;
  margin-top: 5%;
}

.input_number_red {
  float: right;
  border: 1px solid #f759ab;
}

.input_number_blue {
  float: right;
  border: 1px solid #2e77ed;
}

/**index.wxss**/
.audiosBox {
  width: 92%;
  margin: auto;
  height: 130rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f7f7;
  border-radius: 10rpx;
}

/*按钮大小  */
.audioOpen {
  width: 70rpx;
  height: 70rpx;
  border: 2px solid #4c9dee;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 280rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times {
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}

.title view {
  text-indent: 2em;
}

.triangle-downleft {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 700rpx solid #e3f2fd;
  border-left: 0px solid transparent;
  border-right: 400rpx solid transparent;
  height: 0;
  width: 100%;
}

/* pages/subPack/loading/loadPage.wxss */
.container {
  padding-top: 300rpx;
  background-image: linear-gradient(to top,
      #c4c5c7 0%,
      #dcdddf 52%,
      #ebebeb 100%);
  align-items: stretch;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99999;
}

.home_content {
  flex: 1;
  display: flex;
  position: relative;
  z-index: 10;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding-bottom: 450rpx;
  background: -webkit-gradient(linear,
      left top,
      left bottom,
      from(rgba(244, 244, 244, 0)),
      color-stop(0.1, #f4f4f4),
      to(#f4f4f4));
  opacity: 0;
  transform: translate3d(0, 100%, 0);
  animation: rise 3s cubic-bezier(0.19, 1, 0.22, 1) 0.25s forwards;
}

@keyframes rise {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 1;
    transform: translate3d(0, 450rpx, 0);
  }
}

.user_title {
  position: absolute;
  top: 13%;
  left: 50%;
  width: 600rpx;
  height: 200rpx;
  margin-left: -300rpx;
  opacity: 0;
  animation: show 2.5s cubic-bezier(0.19, 1, 0.22, 1) 0.5s forwards;
  text-align: center;
  font-size: 60rpx;
  color: #fff;
  line-height: 200rpx;
  -webkit-text-stroke: 3rpx #fff;
}

.smalltitle {
  position: absolute;
  top: 50rpx;
  left: 50%;
  width: 600rpx;
  height: 200rpx;
  margin-left: -300rpx;
  opacity: 0;
  animation: show 2.5s cubic-bezier(0.19, 1, 0.22, 1) 0.5s forwards;
  text-align: center;
  font-size: 50rpx;
  color: #fff;
  line-height: 200rpx;
  -webkit-text-stroke: 3rpx #666666;
  color: transparent;
  background-clip: text;
  text-shadow: 3rpx 3rpx 3rpx #666666;
  z-index: 99;
}

@keyframes show {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 0.95;
  }
}

.hd {
  position: absolute;
  top: 0%;
  left: 50%;
  width: 1000rpx;
  margin-left: -500rpx;
  height: 200rpx;
  transition: all 0.35s ease;
}

.logo {
  position: absolute;
  z-index: 2;
  left: 50%;
  bottom: 200rpx;
  width: 160rpx;
  height: 160rpx;
  margin-left: -80rpx;
  animation: sway 15s ease-in-out infinite;
  opacity: 0.95;
  overflow: hidden;
  border-radius: 50%;
}

@keyframes sway {
  0% {
    transform: translate3d(0, 20rpx, 0) rotate(-15deg);
  }

  17% {
    transform: translate3d(0, 0rpx, 0) rotate(25deg);
  }

  34% {
    transform: translate3d(0, -20rpx, 0) rotate(-20deg);
  }

  50% {
    transform: translate3d(0, -10rpx, 0) rotate(15deg);
  }

  67% {
    transform: translate3d(0, 10rpx, 0) rotate(-25deg);
  }

  84% {
    transform: translate3d(0, 15rpx, 0) rotate(15deg);
  }

  100% {
    transform: translate3d(0, 20rpx, 0) rotate(-15deg);
  }
}

.wave {
  position: absolute;
  z-index: 3;
  right: 0;
  bottom: 0;
  opacity: 0.725;
  height: 260rpx !important;
  width: 2250rpx !important;
  max-width: none !important;
  animation: wave 10s linear infinite;
}

.wave-bg {
  z-index: 1;
  animation: wave-bg 10.25s linear infinite;
}

@keyframes wave {
  from {
    transform: translate3d(125rpx, 0, 0);
  }

  to {
    transform: translate3d(1125rpx, 0, 0);
  }
}

@keyframes wave-bg {
  from {
    transform: translate3d(375rpx, 0, 0);
  }

  to {
    transform: translate3d(1375rpx, 0, 0);
  }
}

.bd {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  animation: bd-rise 2s cubic-bezier(0.23, 1, 0.32, 1) 0.75s forwards;
  opacity: 0;
  margin-top: 18%;
}

@keyframes bd-rise {
  from {
    opacity: 0;
    transform: translate3d(0, 60rpx, 0);
  }

  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

form {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}

.input-group {
  display: flex;
  align-items: center;
  padding: 25rpx 10rpx;
  margin: 40rpx 3%;
  background: #fff;
  border-radius: 5px;
  border: 2px solid #f4f4f4;
  transition: all 0.25s ease-in-out;
}

.input-group.active {
  border: 2px solid #7acfa6;
}

.input-label {
  color: #888;
  font-size: 13pt;
  height: 25rpx;
  line-height: 25rpx;
  padding: 0 25rpx;
  border-right: 1px solid #d8d8d8;
}

.input-group input,
.input-group picker {
  flex: 1;
  font-size: 13pt;
  min-height: 52rpx;
  height: 52rpx;
  line-height: 52rpx;
  padding: 0 25rpx;
}

.input-placeholder,
.input-group picker.placeholder {
  color: #ccc;
}

.login-help {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 30rpx;
  font-size: 10pt;
  color: #bbb;
}

.login-help-img {
  width: 11pt;
  height: 11pt;
  margin: 0 5rpx;
}

.confirm-logo {
  font-size: 13pt;
  line-height: 200px;
  height: 85rpx;
  text-align: center;
}

.confirm-btn button {
  font-size: 30rpx;
  line-height: 85rpx;
  height: 85rpx;
  background: #999999;
  color: #fff;
  text-align: center;
  border-radius: 50px;
  margin: 50% 20% 30% 20%;
}

.help {
  position: absolute;
  z-index: 100;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.box {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  width: 80%;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 50px rgba(22, 22, 22, 0.35);
  transform: translate3d(0, -400rpx, 0);
}

.box-hd {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  border-bottom: 1px solid #eee;
}

.box-title {
  font-size: 13pt;
}

.box-close {
  position: absolute;
  right: 20rpx;
  width: 35rpx;
  height: 35rpx;
  padding: 15rpx;
}

.box-bd {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15rpx 40rpx 30rpx;
}

.help-q {
  color: #999;
  font-size: 11pt;
  line-height: 200%;
  margin-top: 5rpx;
}

.help-a {
  text-indent: 1em;
  line-height: 160%;
  display: flex;
  flex-direction: column;
}

.help-a text {
  word-break: break-all;
}

/**登录动画**/
.login_video {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  width: 750rpx;
  margin-left: -375rpx;
  height: 1334rpx;
  margin-top: -667rpx;
}

.video_hidden {
  visibility: hidden;
}

page .wx-video-bar {
  display: none;
}

.copyright {
  font-size: 28rpx;
  color: #999;
  position: fixed;
  top: 85%;
  left: 0;
  right: 0;
  padding: 30rpx;
  text-align: center;
}

.copyright text {
  font-size: 28rpx;
  color: #118fff;
  margin-left: 15rpx;
}

/* 新增样式 */
.user-avatar-enhanced {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border: 3px solid #fff;
  transition: all 0.3s ease;
}

.user-avatar-enhanced:hover {
  transform: scale(1.05);
}

.user-name-section {
  background: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.personalize-btn {
  transition: all 0.3s ease;
}

.personalize-btn:hover {
  transform: translateY(-2px);
}

.stats-container {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  padding: 30rpx 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 100rpx;
  margin: 10rpx;
}

.stat-item {
  transition: all 0.3s ease;
  text-align: center;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-label {
  color: #666;
  font-weight: 500;
}

.stat-number {
  font-weight: 700;
  font-size: 18px;
  color: #000;
  margin-top: 15rpx
}

.follow-btn,
.followed-btn,
.settings-btn {
  transition: all 0.3s ease;
}

.follow-btn:hover,
.settings-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.followed-btn:hover {
  background: #eee;
}

.interaction-buttons {
  margin-top: 15px;
}

.interaction-btn {
  transition: all 0.3s ease;
}

.interaction-btn:hover {
  transform: translateY(-2px);
}

.btn-enhanced {
  padding: 5px 10px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.btn-enhanced:hover {
  background: rgba(0, 0, 0, 0.05);
}

.nav-enhanced {
  display: flex;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  margin: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.nav-tab {
  width: 50%;
  margin: 15rpx;
  border-radius: 20px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  text-align: center;
}

.active-tab {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.dynamic-card {
  transition: all 0.3s ease;
}

.time-display {
  transition: all 0.3s ease;
}

/* 等级信息容器样式 */
.level-info-container {
  padding: 8px 12px;
}

.level-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 15px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s ease;
}


/* 等级标题区域 */
.level-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.level-emoji {
  font-size: 16px;
  margin-right: 6px;
  animation: pulse 2s infinite;
}

.level-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.level-name {
  font-size: 12px;
  color: #667eea;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 经验进度区域 */
.experience-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-emoji {
  font-size: 14px;
  animation: sparkle 1.5s ease-in-out infinite alternate;
}

.experience-text {
  display: flex;
  align-items: center;
  font-size: 11px;
  gap: 2px;
}

.exp-emoji {
  font-size: 12px;
  margin-right: 4px;
  animation: bounce 2s infinite;
}

.exp-current {
  color: #667eea;
  font-weight: 600;
}

.exp-separator {
  color: #a9a9a9;
  margin: 0 2px;
}

.exp-next {
  color: #a9a9a9;
  font-weight: 500;
}

/* 动画效果 */
@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

@keyframes sparkle {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }

  100% {
    transform: scale(1.2) rotate(10deg);
    opacity: 1;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-3px);
  }

  60% {
    transform: translateY(-2px);
  }
}

/* 用户操作工具栏样式 */
.user-actions-toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0%;
  left: 0%;
  top: 36%;
  gap: 15px;
  transition: all 0.3s ease;
}

/* 操作项样式 */
.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px 6px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  min-width: 50px;
}

/* 图标容器 */
.action-icon-container {
  position: relative;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-emoji {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 12px;
  z-index: 2;
  animation: float 2s ease-in-out infinite;
}

.action-icon {
  width: 26px;
  height: 26px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

.action-item:hover .action-icon {
  transform: scale(1.1);
}

/* 标签样式 */
.action-label {
  font-size: 10px;
  color: #666;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  transition: all 0.3s ease;
}



/* 浮动动画 */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }

  50% {
    transform: translateY(-3px) rotate(5deg);
  }
}

/* ==================== 弹窗样式优化 ==================== */

/* 增强弹窗基础样式 */
.enhanced-modal {
  backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0.6);
}

.enhanced-dialog {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

/* 弹窗头部 */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-emoji {
  font-size: 20px;
  animation: bounce 2s infinite;
}

.title-text {
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-icon {
  font-size: 16px;
  font-weight: bold;
}

/* 弹窗主体 */
.modal-body {
  padding: 25px;
}

/* 弹窗底部 */
.modal-footer {
  padding: 20px 25px;
  background: rgba(248, 249, 250, 0.8);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 增强按钮样式 */
.enhanced-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}


.btn-emoji {
  font-size: 16px;
}

/* 按钮类型 */
.primary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.danger-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.cancel-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 1px solid #dee2e6;
}

/* 弹窗动画 */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* ==================== 举报弹窗专用样式 ==================== */
.input-section {
  margin-top: 10px;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.label-emoji {
  font-size: 16px;
}

.enhanced-textarea {
  width: 100%;
  min-height: 120px;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
  background: #fafbfc;
}

.enhanced-textarea:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

/* ==================== 订阅弹窗专用样式 ==================== */
.price-section {
  text-align: center;
  margin-bottom: 25px;
}

.price-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.price-amount {
  font-size: 36px;
  font-weight: bold;
  color: #667eea;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.balance-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.currency-icon {
  width: 24px;
  height: 24px;
}

.balance-text {
  font-size: 14px;
  color: #666;
}

.balance-amount {
  font-size: 16px;
  font-weight: 600;
  color: #667eea;
}

/* ==================== 个人简介弹窗专用样式 ==================== */
.bio-content {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.bio-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  letter-spacing: 0.5px;
}

/* ==================== 礼物弹窗专用样式 ==================== */
.gift-modal-header {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.gift-modal-body {
  padding: 20px 10px 0px 10px;
}

.gift-scroll {
  white-space: nowrap;
  width: 100%;
  display: flex;
}

.gift-list {
  display: flex;
  flex-wrap: nowrap;
  gap: 15px;
  padding: 10px 5px;
}

.gift-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 10px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 100px;
}

.gift-item.selected {
  border-color: #ff9a9e;
  background: linear-gradient(135deg, #fff6f6 0%, #fff0f0 100%);
  box-shadow: 0 10px 25px rgba(255, 154, 158, 0.3);
}

.gift-icon-container {
  position: relative;
  margin-bottom: 10px;
}

.gift-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.selected-emoji {
  position: absolute;
  top: -10px;
  right: -10px;
  font-size: 20px;
  animation: sparkle 1.5s infinite;
  z-index: 10;
}

.gift-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.gift-price {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}

.price-emoji {
  font-size: 14px;
}

.balance-section {
  padding: 16px;
  margin-top: 10px;
}

.balance-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.gift-modal-footer {
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
  background: #f8f9fa;
}

.reward-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
  color: white;
  padding: 12px 30px;
}

.gift-close-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666;
  border: 1px solid #dee2e6;
  padding: 12px 25px;
}

@keyframes sparkle {

  0%,
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }

  50% {
    transform: scale(1.2) rotate(20deg);
    opacity: 0.8;
  }
}
</style>