<template>
    <!-- 首页 -->
    <view style="background-color: #ffffff; padding-bottom: 200rpx; min-height: 1200rpx">
        <block v-for="(item, dataListindex) in new_list" :key="dataListindex">
            <view style="position: relative">
                <image
                    v-if="item.top_time"
                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/home_zhang.png'"
                    style="opacity: 0.6; width: 70px; height: 70px; position: absolute; right: 10%; top: 20%; z-index: 200"
                ></image>
                <view class="cu-list menu-avatar">
                    <view class="cu-item">
                        <view
                            @tap="home_url"
                            data-k="1"
                            :data-user_id="item.user_id"
                            class="cu-avatar round eight"
                            :style="'background-image:url(' + item.user_head_sculpture + ');'"
                        >
                            <view style="z-index: 100" :class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                            <image
                                v-if="item.user_id != 0"
                                class="now_level"
                                style="height: 96rpx; width: 96rpx; position: absolute; max-width: initial"
                                :src="item.avatar_frame"
                            ></image>
                        </view>
                        <view class="content flex-sub" style="left: 130rpx">
                            <view>
                                <view :class="item.user_id != 0 ? item.special : ''" style="font-size: 13px">
                                    {{ item.user_nick_name }}
                                </view>
                                <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx" :src="item.attr.attest.at_icon"></image>
                                <image
                                    v-if="item.user_vip == 1 && item.user_id != 0"
                                    :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                    style="width: 30rpx; height: 30rpx; margin-left: 3px"
                                ></image>
                                <image v-if="item.user_id != 0" mode="heightFix" class="now_level" :src="item.level" style="height: 13px; margin-left: 3px"></image>
                                <image
                                    class="now_level"
                                    mode="heightFix"
                                    v-if="item.wear_merit && item.user_id != 0"
                                    :src="item.wear_merit"
                                    style="height: 13px; margin-left: 3px"
                                ></image>
                            </view>
                            <view class="text-gray text-sm flex">
                                <text
                                    v-if="item.check_qq == 'da' && item.user_id != 0"
                                    style="background-color: #9966ff; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                >
                                    {{ $state.diy.qq_name }}主
                                </text>
                                <text
                                    v-if="item.check_qq == 'xiao' && item.user_id != 0"
                                    style="background-color: #4facfe; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                >
                                    管理
                                </text>
                                <text v-if="item.topping_time == 0 && order_time == 'fatie'" style="font-size: 12px; color: #888888">{{ item.adapter_time }}</text>
                                <text v-if="item.topping_time != 0" style="font-size: 12px; color: #888888">{{ item.adapter_time }}</text>
                                <text v-if="item.topping_time == 0 && order_time == 'huifu' && item.huifu_time != null" style="font-size: 12px; color: #888888">
                                    回复于{{ item.huifu_time }}
                                </text>
                            </view>
                        </view>
                    </view>
                    <view style="position: absolute; right: 14rpx; top: 20rpx">
                        <image
                            v-if="item.red == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/atk.png'"
                            style="width: 60rpx; height: 60rpx"
                        ></image>
                        <image
                            v-if="item.is_buy == 1 && version == 0"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/ff.png'"
                            style="width: 60rpx; height: 60rpx"
                        ></image>
                        <image v-if="item.study_type == 3" :src="http_root + 'addons/yl_welore/web/static/mineIcon/index1/atb.png'" style="width: 60rpx; height: 60rpx"></image>
                    </view>
                </view>
            </view>

            <view style="padding: 0px 20px 0px 15px" v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 4 || item.study_type == 5 || item.study_type == 6">
                <view
                    v-if="item.gambit_id"
                    @tap="gambit_list"
                    :data-id="item.gambit_id"
                    style="font-weight: 300; display: inline-block; background-color: #ededed; border-radius: 20px; padding: 2px 10px 2px 2px; font-size: 12px; margin-bottom: 5px"
                >
                    <image style="width: 20px; height: 20px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                    <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                </view>
                <view
                    @tap="home_url"
                    data-k="3"
                    :data-index="dataListindex"
                    :data-type="item.study_type"
                    :data-id="item.id"
                    class=""
                    :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                >
                    <block v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 6">
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </block>
                    <block v-if="item.study_type == 4 || item.study_type == 5 || item.study_type == 6">
                        <rich-text class="text_num" :nodes="item.study_content"></rich-text>
                    </block>
                </view>
                <view
                    @tap="home_url"
                    data-k="3"
                    :data-index="dataListindex"
                    :data-type="item.study_type"
                    :data-id="item.id"
                    v-if="item.image_part.length > 0"
                    class="modern-image-gallery"
                >
                    <!-- 单张图片 -->
                    <view v-if="item.image_part.length == 1" class="single-image-container">
                        <image
                            v-for="(img, img_index) in item.image_part"
                            :key="img_index"
                            :lazy-load="true"
                            :src="img"
                            class="gallery-image single-image"
                            mode="aspectFill"
                        ></image>
                    </view>

                    <!-- 两张图片 -->
                    <view v-if="item.image_part.length == 2" class="two-images-grid">
                        <image
                            v-for="(img, img_index) in item.image_part"
                            :key="img_index"
                            :lazy-load="true"
                            :src="img"
                            class="gallery-image two-image"
                            mode="aspectFill"
                        ></image>
                    </view>
                    <!-- 三张图片 -->
                    <view v-if="item.image_part.length == 3" class="three-images-grid">
                        <image
                            :lazy-load="true"
                            :src="item.image_part[0]"
                            class="gallery-image three-image-main"
                            mode="aspectFill"
                        ></image>
                        <view class="three-image-side">
                            <image
                                :lazy-load="true"
                                :src="item.image_part[1]"
                                class="gallery-image three-image-sub"
                                mode="aspectFill"
                            ></image>
                            <image
                                :lazy-load="true"
                                :src="item.image_part[2]"
                                class="gallery-image three-image-sub"
                                mode="aspectFill"
                            ></image>
                        </view>
                    </view>

                    <!-- 四张及以上图片（小于9张） -->
                    <view v-if="item.image_part.length >= 4 && item.image_part.length < 9" class="multi-images-grid">
                        <image
                            :lazy-load="true"
                            :src="item.image_part[0]"
                            class="gallery-image multi-image-main"
                            mode="aspectFill"
                        ></image>
                        <view class="multi-image-side">
                            <image
                                :lazy-load="true"
                                :src="item.image_part[1]"
                                class="gallery-image multi-image-sub"
                                mode="aspectFill"
                            ></image>
                            <view class="multi-image-bottom">
                                <image
                                    v-if="item.image_part[2]"
                                    :lazy-load="true"
                                    :src="item.image_part[2]"
                                    class="gallery-image multi-image-small"
                                    mode="aspectFill"
                                ></image>
                                <image
                                    v-if="item.image_part[3]"
                                    :lazy-load="true"
                                    :src="item.image_part[3]"
                                    class="gallery-image multi-image-small"
                                    mode="aspectFill"
                                ></image>
                            </view>
                        </view>
                    </view>

                    <!-- 九宫格布局 -->
                    <view v-if="item.image_part.length >= 9" class="nine-grid">
                        <image
                            v-for="(img, img_index) in item.image_part.slice(0, 9)"
                            :key="img_index"
                            :lazy-load="true"
                            :src="img"
                            class="gallery-image nine-grid-item"
                            mode="aspectFill"
                        ></image>
                    </view>
                </view>
                <view style="clear: both; height: 0"></view>
                <!-- 投票 -->
                <view v-if="item.study_type == 4 || item.study_type == 5" class="vote-container">
                    <view class="vote-content">
                        <view
                            @tap.stop.prevent="home_url"
                            data-k="3"
                            :data-index="dataListindex"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            class="vote-title"
                        >
                            <view :class="'vote-type-badge ' + (item.study_type == 4 ? 'single-choice' : 'multiple-choice')">
                                <text v-if="item.study_type == 4" class="vote-type-text">单选</text>
                                <text v-if="item.study_type == 5" class="vote-type-text">多选</text>
                            </view>
                            <rich-text class="text_num vote-title-text" :nodes="item.study_title"></rich-text>
                        </view>
                        <view class="vote-options">
                            <view class="vote-option-wrapper" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                <view
                                    class="vote-option-item"
                                    @tap.stop.prevent="dian_option"
                                    :data-id="vo_item.id"
                                    :data-key="dataListindex"
                                    :data-index="vo_index"
                                >
                                    <view class="vote-option-content">
                                        <view class="vote-option-text">
                                            {{ vo_item.ballot_name }}
                                        </view>
                                        <view class="vote-option-right">
                                            <text
                                                v-if="voi_item == vo_item.id"
                                                class="vote-check-icon cuIcon-check"
                                                v-for="(voi_item, index) in item.vo_id"
                                                :key="index"
                                            ></text>
                                            <text v-if="item.is_vo_check > 0" class="vote-count">{{ vo_item.voters }}</text>
                                        </view>
                                    </view>
                                </view>

                                <view
                                    v-if="item.is_vo_check > 0"
                                    class="vote-progress-bg"
                                >
                                    <view class="vote-progress-bar" :style="'width:' + vo_item.ratio + '%;'"></view>
                                </view>
                            </view>
                            <view
                                @tap.stop.prevent="home_url"
                                data-k="3"
                                :data-index="dataListindex"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                v-if="item.vo.length > 3"
                                class="vote-more-options"
                            >
                                <text class="vote-more-text">查看全部选项</text>
                                <text class="cuIcon-right vote-more-icon"></text>
                            </view>
                        </view>
                    </view>
                    <view class="vote-info">
                        <view v-if="item.vote_deadline != '' && item.vote_deadline != 0 && item.vote_deadline != -1" class="vote-deadline">
                            <text class="cicon-time vote-info-icon"></text>
                            <text class="vote-info-text">截止时间：{{ item.vote_deadline }}</text>
                        </view>
                        <view v-if="item.vote_deadline == -1" class="vote-deadline expired">
                            <text class="cicon-time vote-info-icon" style="color: #e17055;"></text>
                            <text class="vote-info-text">投票已截止</text>
                        </view>
                        <view class="vote-footer">
                            <view class="vote-participants">
                                <text class="cicon-group vote-info-icon"></text>
                                <text class="vote-info-text">参与人数：{{ item.vo_count }}</text>
                            </view>
                            <view>
                                <button
                                @tap.stop.prevent="vote_do"
                                :data-index="vo_index"
                                :data-key="dataListindex"
                                v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                class="vote-submit-btn"
                            >
                                立即投票
                            </button>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 投票 -->
                <!-- </navigator> -->
            </view>

            <view v-if="item.study_type == 1" class="weui-cells weui-cells_after-title">
                <view style="padding: 0rpx 10px 10px 20px">
                    <view
                        v-if="item.gambit_id"
                        @tap="gambit_list"
                        :data-id="item.gambit_id"
                        style="
                            font-weight: 300;
                            display: inline-block;
                            background-color: #ededed;
                            border-radius: 20px;
                            padding: 2px 10px 2px 2px;
                            font-size: 12px;
                            margin-bottom: 5px;
                        "
                    >
                        <image style="width: 20px; height: 20px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                        <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                    </view>
                    <view
                        @tap="home_url"
                        data-k="3"
                        :data-index="dataListindex"
                        :data-type="item.study_type"
                        :data-id="item.id"
                        :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                    >
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </view>
                </view>
                <view
                    style="
                        margin: 0 auto;
                        overflow: hidden;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 170rpx;
                        width: 90%;
                        background-color: #f6f7f7;
                        border: 1px solid #f0f0f0;
                        border-radius: 10rpx;
                    "
                >
                    <view
                        :style="
                            'background-image: url(' +
                            item.user_head_sculpture +
                            ');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
                        "
                    >
                        <view class="audioOpen" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                            <text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
                        </view>
                        <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                            <text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
                        </view>
                    </view>
                    <view style="width: 75%; padding: 20rpx">
                        <view style="display: flex; justify-content: space-between; align-items: center">
                            <view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ item.user_nick_name }}上传的音乐</view>
                            <view class="times">{{ item.starttime }}</view>
                        </view>
                        <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
                            <view style="font-size: 24rpx; color: #999">{{ item.user_nick_name }}</view>
                            <view>
                                <slider style="width: 170rpx" @change="sliderChange" block-size="12px" step="1" :value="item.offset" :max="item.max" selected-color="#4c9dee" />
                            </view>
                        </view>
                    </view>
                </view>
                <!-- </view> -->
                <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id" class="modern-image-gallery">
                    <!-- 单张图片 -->
                    <view v-if="item.image_part.length == 1" class="single-image-container">
                        <image
                            v-for="(img, img_index) in item.image_part"
                            :key="img_index"
                            :lazy-load="true"
                            :src="img"
                            class="gallery-image single-image"
                            mode="aspectFill"
                        ></image>
                    </view>

                    <!-- 两张图片 -->
                    <view v-if="item.image_part.length == 2" class="two-images-grid">
                        <image
                            v-for="(img, img_index) in item.image_part"
                            :key="img_index"
                            :lazy-load="true"
                            :src="img"
                            class="gallery-image two-image"
                            mode="aspectFill"
                        ></image>
                    </view>

                    <!-- 三张图片 -->
                    <view v-if="item.image_part.length == 3" class="three-images-grid">
                        <image
                            :lazy-load="true"
                            :src="item.image_part[0]"
                            class="gallery-image three-image-main"
                            mode="aspectFill"
                        ></image>
                        <view class="three-image-side">
                            <image
                                :lazy-load="true"
                                :src="item.image_part[1]"
                                class="gallery-image three-image-sub"
                                mode="aspectFill"
                            ></image>
                            <image
                                :lazy-load="true"
                                :src="item.image_part[2]"
                                class="gallery-image three-image-sub"
                                mode="aspectFill"
                            ></image>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 视频 -->

            <view v-if="item.study_type == 2">
                <view style="padding: 0rpx 10px 10px 20px">
                    <view
                        v-if="item.gambit_id"
                        @tap="gambit_list"
                        :data-id="item.gambit_id"
                        style="
                            font-weight: 300;
                            display: inline-block;
                            background-color: #ededed;
                            border-radius: 20px;
                            padding: 2px 10px 2px 2px;
                            font-size: 12px;
                            margin-bottom: 5px;
                        "
                    >
                        <image style="width: 20px; height: 20px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/index_topic.png'"></image>
                        <text style="vertical-align: middle; margin-left: 5px; letter-spacing: 1px">{{ item.gambit_name }}</text>
                    </view>
                    <view
                        @tap="home_url"
                        data-k="3"
                        :data-index="dataListindex"
                        :data-type="item.study_type"
                        :data-id="item.id"
                        :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';font-size:14px;letter-spacing: 2px;font-weight: 400;'"
                    >
                        <rich-text class="text_num" :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                    </view>
                </view>
                <view @tap="home_url" data-k="3" :data-index="dataListindex" :data-type="item.study_type" :data-id="item.id">
                    <view v-if="item.image_part.length > 0" class="grid flex-sub col-1" style="position: relative">
                        <image :src="item.image_part[0]" mode="aspectFill" style="height: 190px; margin: 0 auto; border-radius: 5px"></image>
                        <text class="cuIcon-videofill lg bf" style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"></text>
                    </view>
                    <view
                        v-if="item.image_part.length == null || item.image_part.length == 0"
                        class="bg-black padding radius text-center shadow-blur"
                        style="position: relative; margin: 0 auto; width: 80%; height: 180px; z-index: 100; overflow: hidden; border-radius: 5px; font-size: 16px"
                    >
                        <text class="cuIcon-videofill lg text-white" style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"></text>
                    </view>
                </view>
            </view>

            <!-- 视频 -->

            <!-- 位置 -->

            <!-- 位置 -->

            <view style="clear: both; height: 0"></view>

            <!-- 底部操作 -->

            <!-- 优化后的底部操作栏 -->
            <view class="post-footer">
                <view class="post-footer-left">
                    <!-- 圆形分离设计 - 深灰渐变 -->
                    <view class="realm-tag-circle neutral-gray" @tap="home_url" :data-id="item.tory_id" data-k="2">
                        <view class="icon-circle">
                            <text class="realm-icon-circle cuIcon-discover"></text>
                        </view>
                        <text class="realm-name-circle">{{ item.realm_name }}</text>
                    </view>
                </view>
                <view class="post-footer-right">
                    <view v-if="ad_info.paper_browse_num_hide == 1" class="action-item view-count">
                        <text class="action-icon cicon-eye"></text>
                        <text class="action-text">{{ item.study_heat }}</text>
                    </view>
                    <view class="action-item like-action"
                          @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                          :data-id="item.id"
                          :data-key="dataListindex"
                          :class="{ 'liked': item.is_info_zan }">
                        <text class="action-icon"
                              :class="item.is_info_zan ? 'cuIcon-appreciatefill' : 'cuIcon-appreciate'"
                              :animation="item.animationData_zan"></text>
                        <text class="action-text">{{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}</text>
                    </view>
                    <view class="action-item comment-action"
                          @tap="home_pl"
                          :data-id="item.id"
                          :data-type="item.study_type"
                          :data-key="dataListindex">
                        <text class="action-icon cuIcon-messagefill"></text>
                        <text class="action-text">{{ item.study_repount }}</text>
                    </view>
                </view>
            </view>

            <!-- 底部操作 -->

            <view style="width: 95%; height: 1px; background-color: #eeeeee; margin: 0 auto; border-radius: 5px"></view>

            <view style="padding: 30rpx" v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                <ad :unit-id="ad_info.adunit_id"></ad>
            </view>

            <view v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1" style="width: 100%; height: 5px; background-color: #f7f7fa"></view>
        </block>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
    <!-- 首页 -->
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>
<style scoped>
/* 投票容器样式 */
.vote-container {
    margin-top: 30rpx;
    background: #ffffff;
    border-radius: 24rpx;
    box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
    border: 2px solid #e8f4fd;
    overflow: hidden;
    transition: all 0.3s ease;
}
/* 投票内容区域 */
.vote-content {
    padding: 40rpx 30rpx 30rpx;
}

/* 投票标题样式 */
.vote-title {
    margin-bottom: 30rpx;
    cursor: pointer;
}

.vote-type-badge {
    display: inline-block;
    color: #ffffff;
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    transition: all 0.3s ease;
}

/* 单选徽章 - 蓝色系 */
.vote-type-badge.single-choice {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    box-shadow: 0 4rpx 12rpx rgba(116, 185, 255, 0.4);
}

/* 多选徽章 - 绿色系 */
.vote-type-badge.multiple-choice {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    box-shadow: 0 4rpx 12rpx rgba(0, 184, 148, 0.4);
}

.vote-type-text {
    color: #ffffff;
}

.vote-title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #2d3436;
    line-height: 1.5;
    display: block;
}

/* 投票选项区域 */
.vote-options {
    margin-bottom: 20rpx;
}

.vote-option-wrapper {
    position: relative;
    margin-bottom: 24rpx;
}

.vote-option-wrapper:last-child {
    margin-bottom: 0;
}

.vote-option-item {
    background: #ffffff;
    border-radius: 16rpx;
    border: 2px solid #e8f4fd;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    z-index: 2;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.vote-option-item:active {
    transform: translateY(0);
}

.vote-option-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 28rpx;
    position: relative;
    z-index: 3;
}

.vote-option-text {
    flex: 1;
    font-size: 30rpx;
    color: #1a1a1a;
    font-weight: 600;
    line-height: 1.4;
    margin-right: 20rpx;
}

.vote-option-right {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.vote-check-icon {
    font-size: 32rpx;
    color: #00b894;
    font-weight: bold;
}

.vote-count {
    font-size: 26rpx;
    color: #ffffff;
    font-weight: 600;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(116, 185, 255, 0.3);
}

/* 进度条样式 */
.vote-progress-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16rpx;
    overflow: hidden;
    z-index: 1;
}

.vote-progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    border-radius: 16rpx;
    transition: width 0.8s ease;
    opacity: 0.6;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 查看更多选项 */
.vote-more-options {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    text-align: center;
    margin-top: 16rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
}

.vote-more-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
}

.vote-more-icon {
    font-size: 24rpx;
    color: #ffffff;
}

/* 投票信息区域 */
.vote-info {
    background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
    padding: 32rpx;
    border-top: 2px solid #e8f4fd;
}

.vote-deadline {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    gap: 12rpx;
}

.vote-deadline.expired .vote-info-text {
    color: #e17055;
}

.vote-info-icon {
    font-size: 34rpx;
}

.vote-info-text {
    font-size: 26rpx;
    color: #636e72;
    font-weight: 500;
}

.vote-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vote-participants {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

/* 投票按钮 */
.vote-submit-btn {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: #ffffff;
    border: none;
    border-radius: 24rpx;
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 20rpx rgba(232, 67, 147, 0.3);
    line-height: 1;
}

.vote-submit-btn:active {
    transform: translateY(0);
}

.vote-submit-btn::after {
    display: none;
}

/* 优化后的底部操作栏样式 */
.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 10rpx;
    margin: 10px 15px 10px 10px;
    transition: all 0.3s ease;
}

.post-footer-left {
    flex: 1;
}

.post-footer-right {
    display: flex;
    align-items: center;
    gap: 32rpx;
}

/* 话题标签样式 - 改为蓝绿色渐变 */
.realm-tag {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: #ffffff;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 16rpx rgba(116, 185, 255, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.realm-tag:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(116, 185, 255, 0.4);
}

.realm-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
    vertical-align: middle;
}

.realm-name {
    font-weight: 500;
    letter-spacing: 0.5rpx;
    vertical-align: middle;
}

/* 操作按钮样式 */
.action-item {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-item:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.05);
}

.action-icon {
    font-size: 32rpx;
    margin-right: 8rpx;
    transition: all 0.3s ease;
}

.action-text {
    font-size: 26rpx;
    font-weight: 500;
    color: #666666;
    transition: all 0.3s ease;
}

/* 浏览量样式 */
.view-count .action-icon {
    color: #74b9ff;
}

.view-count .action-text {
    color: #74b9ff;
}

/* 点赞按钮样式 */
.like-action .action-icon {
    color: #fd79a8;
}

.like-action .action-text {
    color: #fd79a8;
}

.like-action.liked .action-icon {
    color: #e84393;
    animation: heartbeat 0.6s ease-in-out;
}

.like-action.liked .action-text {
    color: #e84393;
    font-weight: 600;
}

/* 评论按钮样式 */
.comment-action .action-icon {
    color: #00b894;
}

.comment-action .action-text {
    color: #00b894;
}

/* 心跳动画 */
@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

/* 圆形图标 + 文字分离 - 深灰渐变设计 */
.realm-tag-circle {
    display: inline-flex;
    align-items: center;
    gap: 12rpx;
    transition: all 0.3s ease;
    cursor: pointer;
}

.realm-tag-circle:active {
    transform: scale(0.95);
}

.icon-circle {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 20rpx rgba(99, 110, 114, 0.3);
    transition: all 0.3s ease;
}

.realm-tag-circle:active .icon-circle {
    transform: scale(0.9);
    box-shadow: 0 3rpx 10rpx rgba(99, 110, 114, 0.4);
}

.realm-icon-circle {
    font-size: 28rpx;
    color: #ffffff;
}

.realm-name-circle {
    font-size: 24rpx;
    font-weight: 500;
    letter-spacing: 0.5rpx;
    background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 现代化图片画廊样式 */
.modern-image-gallery {
    margin: 20rpx 0;
    padding: 0 20rpx;
}

/* 通用图片样式 */
.gallery-image {
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.gallery-image:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

/* 单张图片 */
.single-image-container {
    width: 100%;
}

.single-image {
    width: 100%;
    height: 400rpx;
    object-fit: cover;
}

/* 两张图片网格 */
.two-images-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12rpx;
}

.two-image {
    width: 100%;
    height: 240rpx;
    object-fit: cover;
}

/* 三张图片布局 */
.three-images-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 12rpx;
    height: 300rpx;
}

.three-image-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.three-image-side {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
}

.three-image-sub {
    width: 100%;
    height: calc(50% - 6rpx);
    object-fit: cover;
}

/* 多张图片布局（4-8张） */
.multi-images-grid {
    display: grid;
    grid-template-columns: 1.8fr 1fr;
    gap: 12rpx;
    height: 320rpx;
}

.multi-image-main {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.multi-image-side {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
}

.multi-image-sub {
    width: 100%;
    height: 60%;
    object-fit: cover;
}

.multi-image-bottom {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8rpx;
    height: calc(40% - 12rpx);
}

.multi-image-small {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 九宫格布局 */
.nine-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8rpx;
}

.nine-grid-item {
    width: 100%;
    height: 200rpx;
    object-fit: cover;
}
</style>
