<template>
  <view class="wallet-container">

    <cu-custom bgColor="none" :isSearch="false" :isBack="true">
      <view slot="backText">返回</view>
      <view slot="content" class="header-title">我的钱包</view>
    </cu-custom>

    <!-- 余额卡片区域 -->
    <view class="balance-cards">
      <!-- 主货币卡片 -->
      <view class="balance-card primary-card">
        <view class="card-header">
          <view class="balance-label">
            <text class="balance-emoji">💎</text>
            <text class="balance-text">{{ design.currency }}余额</text>
          </view>
          <view class="card-actions">
            <navigator v-if="setting.open_withdrawals == 1" url="/yl_welore/pages/packageC/withdrawal/index"
              hover-class="none">
              <view v-if="copyright.recharge_arbor == 1" class="action-btn withdraw-btn" style="padding: 10rpx 18rpx;">
                <text class="btn-text">提现</text>
              </view>
            </navigator>
          </view>
        </view>

        <view class="balance-amount-section">
          <view class="amount-display">
            <text class="amount-number">{{ user_info.conch }}</text>
            <text class="amount-unit">{{ design.currency }}</text>
          </view>
          <view v-if="fraction_scale.currency_redemption_channel == 1" @tap="dh_confer"
            class="exchange-btn primary-exchange">
            <text class="exchange-text">兑换{{ design.confer }}</text>
          </view>
        </view>

        <view class="card-footer">
          <view v-if="copyright.recharge_arbor == 1" @tap="tab_tos" class="info-text">
            <text class="info-emoji">📱</text>
            <text>请您授权手机后进行操作</text>
          </view>
          <view v-if="copyright.recharge_arbor == 0" class="info-text">
            <text class="info-emoji">🎁</text>
            <text>{{ design.currency }}可以兑换会员和福利</text>
          </view>
          <view v-if="copyright.recharge_arbor == 1" @tap="get_pay" class="recharge-btn primary-recharge">
            <text class="recharge-text">获取{{ design.currency }}</text>
          </view>
        </view>
      </view>

      <!-- 积分卡片 -->
      <view class="balance-card secondary-card">
        <view class="card-header">
          <view class="balance-label">
            <text class="balance-emoji">⭐</text>
            <text class="balance-text">{{ design.confer }}余额</text>
          </view>
        </view>

        <view class="balance-amount-section">
          <view class="amount-display">
            <text class="amount-number">{{ user_info.fraction }}</text>
            <text class="amount-unit">{{ design.confer }}</text>
          </view>
          <view v-if="fraction_scale.fraction_redemption_channel == 1" @tap="yes_mod_show"
            class="exchange-btn secondary-exchange">
            <text class="exchange-text">兑换{{ design.currency }}</text>
          </view>
        </view>

        <view class="card-footer">
          <view @tap="tab_tos" class="info-text">
            <text class="info-emoji">💡</text>
            <text>{{ design.confer }}可以兑换{{ design.currency }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- Tab切换区域 -->
    <view class="tab-container">
      <view class="tab-wrapper">
        <view :class="'tab-item ' + (current == 'tab1' ? 'active' : '')" @tap="handleChange" data-key="tab1">
          <text class="tab-text">{{ design.currency }}明细</text>
        </view>
        <view :class="'tab-item ' + (current == 'tab2' ? 'active' : '')" @tap="handleChange" data-key="tab2">
          <text class="tab-text">{{ design.confer }}明细</text>
        </view>
      </view>
    </view>

    <!-- 明细列表区域 -->
    <view class="transaction-list">
      <view class="transaction-item" v-for="(item, index) in (amount_list)" :key="index">
        <view class="transaction-info">
          <view class="transaction-title">
            <text class="transaction-emoji">📝</text>
            <text class="title-text">{{ item.solution }}</text>
          </view>
          <view class="transaction-time">
            <text class="time-emoji">🕐</text>
            <text class="time-text">{{ item.ruins_time }}</text>
          </view>
        </view>
        <view class="transaction-amount">
          <view v-if="item.finance > 0" class="amount-positive">
            <text class="amount-symbol">+</text>
            <text class="amount-value">{{ item.finance }}</text>
          </view>
          <view v-if="item.finance < 0" class="amount-negative">
            <text class="amount-value">{{ item.finance }}</text>
          </view>
          <view v-if="item.finance == 0" class="amount-zero">
            <text class="amount-symbol">-</text>
            <text class="amount-value">{{ item.finance }}</text>
          </view>
        </view>
      </view>

      <!-- 加载状态 -->
      <view class="load-status">
        <view v-if="!di_msg" class="loading-indicator">
          <text class="loading-emoji">⏳</text>
          <text class="loading-text">加载中...</text>
        </view>
        <view v-else class="load-complete">
          <text class="complete-emoji">✅</text>
          <text class="complete-text">已加载全部</text>
        </view>
      </view>
    </view>





    <!-- 充值弹窗 -->
    <view :class="'modal-overlay ' + (pay ? 'show' : '')" @tap="no_pay">
      <view class="recharge-modal" catchtap>
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-text">充值{{ design.currency }}</text>
          </view>
          <view class="modal-subtitle">选择充值金额</view>
        </view>

        <view class="amount-options">
          <view @tap="get_pay_money" :data-index="m_index"
            :class="'amount-option ' + (money_index == m_index ? 'selected' : '')" v-for="(item, m_index) in (pay_money)"
            :key="m_index">
            <view v-if="m_index != 0" class="option-content">
              <view class="option-label">
                <text class="option-emoji">💰</text>
                <text class="label-text">{{ item.money }}{{ design.currency }}</text>
              </view>
              <view class="option-price">
                <text class="currency-symbol">￥</text>
                <text class="price-value">{{ item.money }}</text>
              </view>
            </view>
            <view v-if="m_index == 0" class="option-content custom-option">
              <view class="option-label">
                <text class="label-text">自定义金额</text>
              </view>
              <view class="option-price custom-input">
                <text class="currency-symbol">￥</text>
                <input v-if="pay" @input="set_this_money" :value="item.money" placeholder="输入金额" type="digit"
                  class="custom-amount-input" />
              </view>
            </view>
          </view>
        </view>

        <view class="modal-actions">
          <view class="action-btn cancel-btn" @tap="no_pay">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm-btn" @tap="pay_submit">
            <text class="btn-text">确认充值</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 货币兑换积分弹窗 -->
    <view :class="'modal-overlay ' + (dh_confer_t ? 'show' : '')">
      <view class="exchange-modal">
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-text">兑换{{ design.confer }}</text>
          </view>
          <view class="close-btn" @tap="hideModal">
            <text class="close-emoji">❌</text>
          </view>
        </view>

        <view class="exchange-content">
          <view class="input-section">
            <view class="input-label">
              <text class="label-emoji">💎</text>
              <text class="label-text">输入{{ design.currency }}数量</text>
            </view>
            <view class="input-wrapper">
              <input v-if="dh_confer_t" @input="get_num" :value="bei_money" type="digit" class="exchange-input"
                placeholder="请输入兑换数量" />
            </view>
          </view>

          <view class="result-section">
            <view class="result-label">
              <text class="result-emoji">⭐</text>
              <text class="result-text">将获得：</text>
            </view>
            <view class="result-amount">
              <text class="result-number">{{ ji_money }}</text>
              <text class="result-unit">{{ design.confer }}</text>
            </view>
          </view>
        </view>

        <view class="modal-actions">
          <view class="action-btn cancel-btn" @tap="hideModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm-btn" @tap="add_bei_ji">
            <text class="btn-text">确认兑换</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分兑换货币弹窗 -->
    <view :class="'modal-overlay ' + (dh_confer_j ? 'show' : '')">
      <view class="exchange-modal">
        <view class="modal-header">
          <view class="modal-title">
            <text class="title-text">兑换{{ design.currency }}</text>
          </view>
          <view class="close-btn" @tap="hideModal">
            <text class="close-emoji">❌</text>
          </view>
        </view>

        <view class="exchange-content">
          <view class="input-section">
            <view class="input-label">
              <text class="label-emoji">⭐</text>
              <text class="label-text">输入{{ design.confer }}数量</text>
            </view>
            <view class="input-wrapper">
              <input v-if="dh_confer_j" @input="get_num_b" :value="bei_money_b" type="digit" class="exchange-input"
                placeholder="请输入兑换数量" />
            </view>
          </view>

          <view class="result-section">
            <view class="result-label">
              <text class="result-emoji">💎</text>
              <text class="result-text">将获得：</text>
            </view>
            <view class="result-amount">
              <text class="result-number">{{ ji_money_b }}</text>
              <text class="result-unit">{{ design.currency }}</text>
            </view>
          </view>
        </view>

        <view class="modal-actions">
          <view class="action-btn cancel-btn" @tap="hideModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="action-btn confirm-btn" @tap="get_ji_bei">
            <text class="btn-text">确认兑换</text>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageC/user_details/tofix.wxs"></script> -->
<script>
var app = getApp();
import http from "../../../util/http.js";
import md5 from "../../../util/md5.js";
export default {
  /**
   * 页面的初始数据
   */
  data() {
    return {
      current: 'tab1',
      fraction_scale: {},
      user_info: {},
      pay_list: [],
      pay_index: 0,
      pay_info: {},
      pay: false,
      animationPay: {},
      pay_money: [{
        money: 1
      }, {
        money: 6
      }, {
        money: 30
      }, {
        money: 68
      }, {
        money: 168
      }, {
        money: 328
      }, {
        money: 648
      }],
      money_index: 0,
      amount_list: [],
      page: 1,
      di_msg: false,
      yes_mod: false,
      withdraw: false,
      withdraw_number: '',
      dh_confer_t: false,
      dh_confer_j: false,
      ji_money: '0.00',
      bei_money: '',
      ji_money_b: '0.00',
      bei_money_b: '',
      ios: false,
      scale: 10,
      jibei_button: true,
      beiji_button: true,
      chenck_phone: false,
      copyright: {},
      design: {},
      dd_fraction: 0,
      bei_ji: 0,
      bei_ji_b: 0,
      setting:{},
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var e = app.globalData.getCache("userinfo");
    var that = this;
    if (!e) {
      uni.login({
        success(res) {
          var params = new Object();
          params.code = res.code;
          http.POST(app.globalData.api_root + 'Login/index', {
            params: params,
            success: function (open) {
              console.log(open);
              var data = new Object();
              data.openid = open.data.info.openid;
              data.session_key = open.data.info.session_key;
              http.POST(app.globalData.api_root + 'Login/add_tourist', {
                params: data,
                success: function (d) {
                  app.globalData.setCache("userinfo", d.data.info);
                  that.get_user_info();
                  that.get_user_amount();
                }
              });
            }
          });
        }
      });
    } else {
      that.get_user_info();
      that.get_user_amount();
    }
    this.page = 1;
    this.copyright = getApp().globalData.store.getState().copyright;
    var design = uni.getStorageSync('is_diy');
    if (design) {
      this.design = design;
    } else {
      this.get_diy();
    }
    if (e.user_phone) {
      this.chenck_phone = true;
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var that = this;
    var e = app.globalData.getCache("userinfo");
    //获取系统信息
    uni.getSystemInfo({
      success(res) {
        var copyright = getApp().globalData.store.getState().copyright;
        console.log(res.platform);
        if (res.platform == "ios" && copyright.ios_pay_arbor == 0) {
          that.ios = false;
        }
        if (res.platform == "ios" && copyright.ios_pay_arbor == 1) {
          that.ios = true;
        }
        if (res.platform != "ios") {
          that.ios = true;
        }
      }
    });
    if (e.user_phone) {
      this.chenck_phone = true;
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    //wx.showNavigationBarLoading() //在标题栏中显示加载
    //模拟加载
    setTimeout(function () {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.amount_list = [];
    this.page = 1;
    this.get_user_info();
    this.get_user_amount();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    this.page = this.page + 1;
    this.get_user_amount();
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    console.log(forward);
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/index/index',
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: '您的好友给您发了一条信息',
        path: '/yl_welore/pages/index/index'
      };
    }
  },
  methods: {
    /**
    * 获取手机号
    */
    getPhoneNumber(c) {
      console.log(c);
      if (c.detail.errMsg == 'getPhoneNumber:ok') {
        var b = app.globalData.api_root + 'User/get_user_phone';
        var that = this;
        var e = app.globalData.getCache("userinfo");
        console.log(e);
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.encryptedData = c.detail.encryptedData;
        params.iv = c.detail.iv;
        params.sessionKey = e.session_key;
        http.POST(b, {
          params: params,
          success: function (res) {
            console.log(res);
            if (res.data.status == 'success') {
              var e = app.globalData.getCache("userinfo");
              e.user_phone = res.data.phone;
              console.log(e);
              app.globalData.setCache("userinfo", e);
            }
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.chenck_phone = true;
          },
          fail: function () {
            uni.showModal({
              title: '提示',
              content: '网络繁忙，请稍候重试！',
              showCancel: false,
              success: function (res) { }
            });
          }
        });
      } else {
        uni.showModal({
          title: '提示',
          content: c.detail.errMsg,
          showCancel: false,
          success: function (res) { }
        });
      }
    },
    /**
     * 当前兑换比例
     */
    tab_tos() {
      uni.navigateTo({
        url: '/yl_welore/pages/packageC/service_centre/index'
      });
      // wx.showToast({
      //   title: '当前兑换比例 1：' + this.data.scale,
      //   icon: 'none',
      //   duration: 2000
      // })
    },
    /**
     * 兑换积分
     */
    dh_confer() {
      this.dh_confer_t = true;
    },
    /**
     * 获取输入框
     */
    get_num(d) {
      var money = d.detail.value;
      if (money == '') {
        this.ji_money = '0.00';
        this.bei_money = '';
        return;
      }
      var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
      if (reg.test(money)) {
        //正则匹配通过，提取有效文本
        money = money.replace(reg, '$2$3$4');
      } else {
        //正则匹配不通过，直接清空
        money = '';
      }
      console.log(this.user_info['conch']);
      console.log(money);
      if (parseFloat(money) > parseFloat(this.user_info['conch'])) {
        money = this.user_info['conch'];
      }
      this.ji_money = (money * this.scale).toFixed(2);
      this.bei_money = money;
    },
    /**
     * 获取积分兑换贝壳输入框
     */
    get_num_b(d) {
      var money = d.detail.value;
      console.log(money);
      if (money == '') {
        this.ji_money_b = '0.00';
        this.bei_money_b = '';
        return;
      }
      var reg = /^(\.*)(\d+)(\.?)(\d{0,2}).*$/g;
      if (reg.test(money)) {
        //正则匹配通过，提取有效文本
        money = money.replace(reg, '$2$3$4');
      } else {
        //正则匹配不通过，直接清空
        money = '';
      }
      console.log(this.user_info['fraction']);
      console.log(money);
      if (parseFloat(money) > parseFloat(this.user_info['fraction'])) {
        money = this.user_info['fraction'];
      }
      var b_money = (money / this.scale).toFixed(3);
      var basic = b_money.substr(0, b_money.length - 1);
      this.ji_money_b = basic;
      this.bei_money_b = money;
    },
    /**
     * 贝壳兑换积分
     */
    add_bei_ji() {
      var that = this;
      if (this.beiji_button == false) {
        return;
      }
      if (that.bei_money == '' || that.bei_money <= 0) {
        uni.showToast({
          title: '请填写正确兑换的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.beiji_button = false;
      var b = app.globalData.api_root + 'User/add_bei_ji';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.bei_money = that.bei_money;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.page = 1;
            that.beiji_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.hideModal();
            that.get_user_info();
            that.get_user_amount();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.beiji_button = true;
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 自定义金额
     */
    set_this_money(e) {
      var money = e.detail.value;
      var list = this.pay_money;
      list[0]['money'] = money;
      this.pay_money = list;
    },
    hideModal() {
      this.dh_confer_j = false;
      this.withdraw = false;
      this.withdraw_card = false;
      this.dh_confer_t = false;
      this.ji_money = '0.00';
      this.bei_money = '';
      this.ji_money_b = '0.00';
      this.bei_money_b = '';
    },
    yes_mod_show() {
      this.dh_confer_j = true;
    },
    /**
     * 积分兑换贝壳
     */
    get_ji_bei() {
      if (this.jibei_button == false) {
        return;
      }
      var that = this;
      if (that.bei_money_b == '' || that.bei_money_b <= 0) {
        uni.showToast({
          title: '请填写正确兑换的数量',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      this.jibei_button = false;
      var b = app.globalData.api_root + 'User/get_ji_bei';
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.bei_money_b = that.ji_money_b;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.hideModal();
            that.page = 1;
            that.jibei_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.get_user_info();
            that.get_user_amount();
          } else {
            that.yes_mod = false;
            that.jibei_button = true;
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    handleChange(detail) {
      this.amount_list = [];
      this.page = 1;
      this.current = detail.currentTarget.dataset.key;
      this.get_user_amount();
    },
    /**
     * 充值贝壳
     */
    get_pay() {
      var that = this;
      // 创建一个动画实例
      var animation = uni.createAnimation({
        // 动画持续时间
        duration: 150,
        // 定义动画效果，当前是匀速
        timingFunction: 'linear'
      });
      // 将该变量赋值给当前动画
      that.animation = animation;
      // 先在y轴偏移，然后用step()完成一个动画
      animation.translateY(230).step();
      // 用setData改变当前动画
      that.animationPay = animation.export();
      that.pay = true;
      // 设置setTimeout来改变y轴偏移量，实现有感觉的滑动
      setTimeout(function () {
        animation.translateY(0).step();
        that.animationPay = animation.export();
      }, 100);
    },
    /**
     * 关闭
     */
    no_pay() {
      this.pay = false;
    },
    /**
     * 获取零钱明细
     */
    get_user_amount() {
      var b = app.globalData.api_root + 'User/get_user_amount';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.page = this.page;
      params.evaluate = this.current;
      var allMsg = that.amount_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.amount_list = allMsg;
            that.setting = res.data.setting;
            that.scale = res.data.scale;
            that.fraction_scale = res.data.fraction_scale;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 获取会员信息
     */
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status == 'success') {
            var fraction = (res.data.info['fraction'] / 10).toFixed(3);
            that.user_info = res.data.info;
            that.dd_fraction = fraction.substring(0, fraction.length - 1);
            that.bei_ji = that.copyright['conch_convert'] - res.data.info['bei_ji'] < 0 ? 0 : that.copyright['conch_convert'] - res.data.info['bei_ji'];
            that.bei_ji_b = that.copyright['fraction_convert'] - res.data.info['ji_bei'] < 0 ? 0 : that.copyright['fraction_convert'] - res.data.info['ji_bei'];
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 充值金额
     */
    get_pay_money(op) {
      var index = op.currentTarget.dataset.index;
      this.money_index = index;
    },
    /**
     * 充值
     */
    pay_submit() {
      var money = this.pay_money[this.money_index]['money'];
      console.log(money);
      if (!money) {
        uni.showToast({
          title: '充值金额错误！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.money = money;
      var b = app.globalData.api_root + 'Pay/do_pay';
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.return_msg == "OK") {
            var timeStamp = (Date.parse(new Date()) / 1000).toString();
            var pkg = 'prepay_id=' + res.data.prepay_id;
            var nonceStr = res.data.nonce_str;
            var paySign = md5.hexMD5('appId=' + res.data.appid + '&nonceStr=' + nonceStr + '&package=' + pkg + '&signType=MD5&timeStamp=' + timeStamp + "&key=" + res.data.app_info['app_key']).toUpperCase(); //此处用到hexMD5插件
            //发起支付
            uni.requestPayment({
              'timeStamp': timeStamp,
              'nonceStr': nonceStr,
              'package': pkg,
              'signType': 'MD5',
              'paySign': paySign,
              success: function (res) {
                uni.showToast({
                  title: '充值成功！',
                  icon: 'none',
                  duration: 2000
                });
                that.page = 1;
                that.amount_list = [];
                //支付成功之后的操作
                that.get_user_info();
                that.no_pay();
                that.get_user_amount();
              },
              complete: function () {
                that.page = 1;
                that.amount_list = [];
                //支付成功之后的操作
                that.get_user_info();
                that.no_pay();
                that.get_user_amount();
              }
            });
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.return_msg,
              showCancel: false,
              success: function (res) { }
            });
            that.get_pay();
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      // params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status) {
            return;
          } else {
            that.design = res.data;
            uni.setStorageSync("is_diy", res.data);
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    }
  }
};
</script>
<style>
/* 页面基础样式 */
page {
  background-color: #FAFBFC;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.wallet-container {
  min-height: 100vh;
  background-color: #FAFBFC;
}

/* 头部标题样式 */
.header-title {
  color: #2C3E50;
  font-weight: 600;
  font-size: 36rpx;
}

/* 余额卡片区域 */
.balance-cards {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.balance-card {
  background-color: #FFFFFF;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(91, 155, 213, 0.08);
  border: 1rpx solid rgba(91, 155, 213, 0.1);
  transition: all 0.3s ease;
}

.balance-card:hover {
  box-shadow: 0 8rpx 32rpx rgba(91, 155, 213, 0.12);
  transform: translateY(-2rpx);
}

.primary-card {
  background: #FFFFFF;
  border-left: 6rpx solid #5B9BD5;
}

.secondary-card {
  background: #FFFFFF;
  border-left: 6rpx solid #70AD47;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.balance-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.balance-emoji {
  font-size: 32rpx;
}

.balance-text {
  font-size: 28rpx;
  color: #7B8794;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 10rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 18rpx;
  border-radius: 18rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.withdraw-btn {
  background-color: rgba(255, 167, 38, 0.1);
  color: #FFA726;
  border: 1rpx solid rgba(255, 167, 38, 0.2);
}

.withdraw-btn:active {
  background-color: rgba(255, 167, 38, 0.15);
  transform: scale(0.95);
}

.btn-emoji {
  font-size: 20rpx;
}

.btn-text {
  font-weight: 500;
}

/* 金额显示区域 */
.balance-amount-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 30rpx 0;
}

.amount-display {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.amount-number {
  font-size: 56rpx;
  font-weight: 700;
  color: #5B9BD5;
}

.secondary-card .amount-number {
  color: #70AD47;
}

.amount-unit {
  font-size: 28rpx;
  color: #7B8794;
  font-weight: 500;
}

.exchange-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.primary-exchange {
  background-color: rgba(91, 155, 213, 0.1);
  color: #5B9BD5;
  border: 1rpx solid rgba(91, 155, 213, 0.2);
}

.secondary-exchange {
  background-color: rgba(112, 173, 71, 0.1);
  color: #70AD47;
  border: 1rpx solid rgba(112, 173, 71, 0.2);
}

.exchange-btn:active {
  transform: scale(0.95);
}

.exchange-emoji {
  font-size: 20rpx;
}

.exchange-text {
  font-weight: 500;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.info-text {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 24rpx;
  color: #7B8794;
}

.info-emoji {
  font-size: 20rpx;
}

.recharge-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 18rpx;
  border-radius: 18rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.primary-recharge {
  background-color: rgba(91, 155, 213, 0.1);
  color: #5B9BD5;
  border: 1rpx solid rgba(91, 155, 213, 0.2);
}

.recharge-btn:active {
  transform: scale(0.95);
}

.recharge-emoji {
  font-size: 20rpx;
}

.recharge-text {
  font-weight: 500;
}

/* Tab切换样式 */
.tab-container {
  background-color: #FAFBFC;
  padding: 0 20rpx;
  margin-top: 20rpx;
}

.tab-wrapper {
  display: flex;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 6rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(91, 155, 213, 0.08);
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #7B8794;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item.active {
  background-color: #5B9BD5;
  color: #FFFFFF;
  box-shadow: 0 4rpx 12rpx rgba(91, 155, 213, 0.2);
}

.tab-emoji {
  font-size: 24rpx;
}

.tab-text {
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 6rpx;
  background-color: #5B9BD5;
  border-radius: 3rpx;
}

/* 交易列表样式 */
.transaction-list {
  padding: 20rpx;
  background-color: #FAFBFC;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(91, 155, 213, 0.06);
  border: 1rpx solid rgba(91, 155, 213, 0.08);
  transition: all 0.3s ease;
}

.transaction-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(91, 155, 213, 0.12);
}

.transaction-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.transaction-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.transaction-emoji {
  font-size: 24rpx;
}

.title-text {
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
}

.transaction-time {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.time-emoji {
  font-size: 20rpx;
}

.time-text {
  font-size: 24rpx;
  color: #7B8794;
}

.transaction-amount {
  display: flex;
  align-items: center;
}

.amount-positive {
  display: flex;
  align-items: center;
  color: #70AD47;
  font-weight: 600;
  font-size: 32rpx;
}

.amount-negative {
  display: flex;
  align-items: center;
  color: #E74C3C;
  font-weight: 600;
  font-size: 32rpx;
}

.amount-zero {
  display: flex;
  align-items: center;
  color: #7B8794;
  font-weight: 600;
  font-size: 32rpx;
}

.amount-symbol {
  margin-right: 4rpx;
}

.amount-value {
  font-weight: 600;
}

/* 加载状态样式 */
.load-status {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 20rpx;
  margin-top: 20rpx;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #5B9BD5;
  font-size: 28rpx;
}

.loading-emoji {
  font-size: 32rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-weight: 500;
}

.load-complete {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #70AD47;
  font-size: 28rpx;
}

.complete-emoji {
  font-size: 32rpx;
}

.complete-text {
  font-weight: 500;
}

/* 弹窗基础样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.recharge-modal,
.exchange-modal {
  background-color: #ffffff;
  border-radius: 24rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.modal-overlay.show .recharge-modal,
.modal-overlay.show .exchange-modal {
  transform: scale(1);
}

/* 弹窗头部样式 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.title-emoji {
  font-size: 36rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.modal-subtitle {
  font-size: 24rpx;
  color: #7B8794;
  margin-top: 8rpx;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgba(231, 76, 60, 0.1);
  transition: all 0.3s ease;
}

.close-btn:active {
  background-color: rgba(231, 76, 60, 0.15);
  transform: scale(0.9);
}

.close-emoji {
  font-size: 24rpx;
}

/* 充值金额选项样式 */
.amount-options {
  padding: 30rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.amount-option {
  border: 2rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.amount-option.selected {
  border-color: #5B9BD5;
  background-color: rgba(91, 155, 213, 0.08);
  transform: scale(1.02);
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  align-items: center;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 26rpx;
  color: #7B8794;
  font-weight: 500;
}

.option-emoji {
  font-size: 24rpx;
}

.label-text {
  font-weight: 500;
}

.option-price {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
  font-size: 36rpx;
  font-weight: 700;
  color: #5B9BD5;
}

.currency-symbol {
  font-size: 24rpx;
  color: #7B8794;
}

.price-value {
  font-weight: 700;
}

.custom-option {
  grid-column: span 2;
}

.custom-input {
  width: 100%;
}

.custom-amount-input {
  flex: 1;
  font-size: 36rpx;
  font-weight: 700;
  color: #5B9BD5;
  text-align: center;
  border: none;
  outline: none;
  background: transparent;
}

/* 兑换弹窗样式 */
.exchange-content {
  padding: 30rpx;
}

.input-section {
  margin-bottom: 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #2C3E50;
  font-weight: 500;
}

.label-emoji {
  font-size: 24rpx;
}

.label-text {
  font-weight: 500;
}

.input-wrapper {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 4rpx;
}

.exchange-input {
  width: 100%;
  padding: 20rpx;
  font-size: 32rpx;
  color: #2C3E50;
  background-color: transparent;
  border: none;
  outline: none;
  text-align: center;
  font-weight: 600;
  height: 100rpx;
}

.result-section {
  background-color: rgba(91, 155, 213, 0.08);
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.result-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  color: #7B8794;
}

.result-emoji {
  font-size: 24rpx;
}

.result-text {
  font-weight: 500;
}

.result-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8rpx;
}

.result-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #5B9BD5;
}

.result-unit {
  font-size: 28rpx;
  color: #7B8794;
  font-weight: 500;
}

/* 弹窗按钮样式 */
.modal-actions {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background-color: #F8F9FA;
  color: #7B8794;
  border: 1rpx solid #E9ECEF;
}

.cancel-btn:active {
  background-color: #E9ECEF;
  transform: scale(0.95);
}

.confirm-btn {
  background-color: #5B9BD5;
  color: #FFFFFF;
  border: 1rpx solid #5B9BD5;
}

.confirm-btn:active {
  background-color: #4A8BC2;
  transform: scale(0.95);
}

.btn-emoji {
  font-size: 24rpx;
}

.btn-text {
  font-weight: 600;
}
</style>