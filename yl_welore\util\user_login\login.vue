<template>
    <view v-if="check_user_login" :class="'cu-modal ' + (check_user_login ? 'show' : '')">
        <view class="cu-dialog">
            <view class="cu-bar bg-white justify-end">
                <view class="content">设置昵称</view>
                <view class="action" @tap="hideModal">
                    <text class="cuIcon-close text-red"></text>
                </view>
            </view>
            <form @submit="formSubmit">
                <view class="padding-xl">
                    <view style="width: 80px; height: 80px; margin: 0 auto; position: relative">
                        <button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" hover-class="none" style="position: relative">
                            <image v-if="!avatarUrl" :src="http_root + 'addons/yl_welore/web/static/applet_icon/default.png'" style="width: 80px; height: 80px"></image>
                            <image v-if="avatarUrl" :src="avatarUrl" style="width: 80px; height: 80px; border-radius: 50%"></image>
                            <image src="/static/yl_welore/style/icon/xiangji.png" style="width: 46rpx; height: 46rpx; position: absolute; right: 0; bottom: 0"></image>
                        </button>
                    </view>
                    <view class="cu-form-group margin-top-xl">
                        <view class="title">昵称</view>
                        <input name="nickname" type="nickname" @tap.stop.prevent="a" placeholder="起个漂亮的名字~" />
                    </view>
                    <view class="padding flex flex-direction margin-top-lg">
                        <button style="width: 100%;" form-type="submit" class="cu-btn bg-yellow text-white margin-tb-sm lg round">保存</button>
                    </view>
                </view>
            </form>
        </view>
    </view>
</template>

<script>
// tabBarComponent/tabBar.js
var app = getApp();
var http = require('../http.js');

export default {
    /**
     * 组件的属性列表
     */
    props: {
        check_user_login: {
            type: Boolean,
            default: false
        }
    },
    /**
     * 组件的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            avatarUrl: ''
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        hideModal() {
            this.check_user_login = false;
        },
        onChooseAvatar(d) {
            console.log(123123);
            console.log(d);
            uni.showLoading({
                title: '上传中...'
            });
            var that = this;
            var avatarUrl = d.detail.avatarUrl;
            var e = app.globalData.getCache('userinfo');
            uni.uploadFile({
                url: app.globalData.api_root + 'User/img_upload',
                filePath: avatarUrl,
                name: 'sngpic',
                header: {
                    'content-type': 'multipart/form-data'
                },
                formData: {
                    token: e.token,
                    openid: e.openid,
                    much_id: app.globalData.siteInfo.uniacid
                },
                success: (res) => {
                    uni.hideLoading();
                    console.log(res);
                    var data = JSON.parse(res.data);
                    console.log(data);
                    uni.showToast({
                        title: data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    if (data.status == 'success' || data.code == 1) {
                        that.avatarUrl = data.url;
                    }
                },
                fail: (res) => {
                    uni.hideLoading();
                    uni.showToast({
                        title: res.data.msg,
                        icon: '上传错误！',
                        duration: 2000
                    });
                }
            });
        },
        formSubmit(d) {
            var that = this;
            console.log(d);
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.nike_name = d.detail.value.nickname;
            params.avatarUrl = this.avatarUrl;
            if (this.avatarUrl == '') {
                uni.showToast({
                    title: '上传个头像吧~',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            params.much_id = app.globalData.siteInfo.uniacid;
            http.POST(app.globalData.api_root + 'Login/new_do_login', {
                params: params,
                success: (res) => {
                    console.log(res);
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: (res) => {}
                    });
                    if (res.data.code == 0) {
                        var user_info = res.data.info;
                        console.log(user_info);
                        app.globalData.setCache('userinfo', user_info);
                        that.check_user_login = false;
                        that.$emit('checkPhoen', {
                            detail: 1
                        });
                    }
                },
                fail: () => {}
            });
        }
    }
};
</script>
<style>
button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
}
</style>
