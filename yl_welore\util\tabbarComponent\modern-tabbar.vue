<!--
 * @Description: 现代化自定义悬浮异形tabbar
-->
<template>
	<view class="tqb-tabbar">
		<view class="mark" v-show="visible" @click="handleMarkClick"
			:class="{ 'mark-hide': isClosing, 'mark-show': visible && !isClosing }"
			:style="{ 'background-image': currentBackground }">
			<view class="mark-title">
				<view class="time-container">
					<view class="time">
						<view class="day">{{ currentDate.day }}</view>
						<view class="month">{{ currentDate.month }}</view>
					</view>
					<view class="year">{{ currentDate.year }}</view>
				</view>
				<view class="content_tab">
					<view class="label">说说 记录美好生活</view>
					<view class="subtitle">分享此刻的精彩瞬间</view>
				</view>
				<!-- <view class="decoration">
					<view class="dot"></view>
					<view class="line"></view>
				</view> -->
			</view>
			<view class="mak-box" :style="{ 'justify-content': version == 1 ? 'center' : 'start' }">
				<view class="box" v-for="(item, index) in handleList" :key="index" @click="handleClick(item.url)"
					:style="{ 'animation-delay': (index * 0.1) + 's' }">
					<view class="img-view">
						<image :src="item.imageUrl" mode=""></image>
						<view v-if="item.vip == 1" class="vip-badge">VIP</view>
					</view>
					<text>{{ item.text }}</text>
				</view>
				<view class="close" @click="handleClose">
					<text class="_icon-close-round" style="font-size: 80rpx;"></text>
				</view>
			</view>
		</view>
		<view class="tqb-tabbar-body">
			<view class="tqb-tabbar-item" v-for="(item, index) in computedTabBarList" :key="index"
				@click="switchClick(item, index)">
				<view v-if="floorstatus && index == 0">
					<image @tap.stop.prevent="goTop" class="tabbar_icon animation-slide-bottom"
						:src="(http_root) + 'addons/yl_welore/web/static/applet_icon/tag_top.png'"></image>
					<text style="color: #333333;" class="animation-slide-bottom">返回顶部</text>
				</view>
				<view v-if="index !== 2" class="tabbar-item-body">
					<view class="tabbar-item-icon">
						<image :src="activeIndex == index ? item.selectedIconPath : item.iconPath"
							style="width: 56rpx; height: 56rpx;"></image>
					</view>
					<view
						:style="{ 'font-size: 24rpx;color': activeIndex == index ? tabbar.selectedColor : tabbar.color }">
						{{ item.text }}</view>
				</view>
				<view v-else class="add-icon">
					<image :src="item.iconPath" style="width: 88rpx; height: 88rpx;"></image>
				</view>
			</view>
		</view>
		<login id="login" @checkPhoen="check_user_login = false;" :check_user_login="check_user_login"></login>
		<phone id="phone" @close_phone_modal="check_phone_show = false;" :check_phone="check_phone_show"></phone>
	</view>
</template>

<script>
var app = getApp();
var http = require("@/yl_welore/util/http.js");
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
export default {
	components: {
		phone,
		login,
	},
	props: {
		activeIndex: {
			type: Number,
			default: 0
		},
		tabbar: {
			type: Object,
			default: () => ({})
		},
		floorstatus: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			check_user_login: false,
			check_phone_show: false,
			user_info: {},
			tabBarList: [],
			handleList: [],
			visible: false,
			diy: {},
			version: 1,
			add: {},
			http_root: app.globalData.http_root,
			// 背景颜色数组
			backgroundColors: [
				'linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);',
				'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);',
				'linear-gradient(135deg, #fdfcfb 0%, #e2d1c3 100%);',
				'linear-gradient(to top, #e6e9f0 0%, #eef1f5 100%);',
				'linear-gradient(to top, #accbee 0%, #e7f0fd 100%);',
				'linear-gradient(to top, #d5d4d0 0%, #d5d4d0 1%, #eeeeec 31%, #efeeec 75%, #e9e9e7 100%);',
				'linear-gradient(to top, #c4c5c7 0%, #dcdddf 52%, #ebebeb 100%);',
				'linear-gradient(-225deg, #FFFEFF 0%, #D7FFFE 100%);',
				'linear-gradient(-225deg, #E3FDF5 0%, #FFE6FA 100%);',
				'linear-gradient(to top, #cd9cf2 0%, #f6f3ff 100%);'
			],
			// 当前选中的背景色
			currentBackground: '',
			// 关闭动画控制
			isClosing: false
		}
	},
	computed: {
		// 根据传入的tabbar数据动态生成tabBarList
		computedTabBarList() {
			if (this.tabbar && this.tabbar.list && this.tabbar.list.length > 0) {
				return this.tabbar.list.map((item, index) => {
					if (index === 2) {
						// 中间的特殊按钮
						return {
							text: item.text || '发布',
							iconPath: item.iconPath || '/static/yl_welore/style/icon/home_add.png',
							isSpecial: true
						}
					} else {
						return {
							home: item.home_s,
							text: item.text,
							iconPath: item.iconPath,
							selectedIconPath: item.selectedIconPath,
							pagePath: item.pagePath,
							isSpecial: false
						}
					}
				});
			}
			return this.tabBarList;
		},
		// 获取当前日期
		currentDate() {
			const now = new Date();
			const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
			return {
				day: String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0'),
				month: months[now.getMonth()],
				year: now.getFullYear()
			};
		}
	},
	watch: {
		visible: function (d) {
			// todo anything
		}
	},
	mounted() {
		console.log(this.tabbar);
		// 初始化随机背景色
		this.getRandomBackground();
		// 组件挂载时动态生成handleList
		setTimeout(() => {
			this.generateHandleList();
			this.get_user_info();
		}, 1500)
	},
	methods: {
		// 随机选择背景颜色
		getRandomBackground() {
			const randomIndex = Math.floor(Math.random() * this.backgroundColors.length);
			this.currentBackground = this.backgroundColors[randomIndex];
		},
		// 处理 mark 点击事件（背景点击关闭）
		handleMarkClick() {
			this.startCloseAnimation();
		},
		// 开始关闭动画
		startCloseAnimation() {
			this.isClosing = true;
			// 400ms 后隐藏元素（与 CSS 动画时间一致）
			setTimeout(() => {
				this.visible = false;
				this.isClosing = false;
				if (this.activeIndex == 1) {
					uni.$emit('hideCanvas', false);
				}
			}, 400);
		},
		// 根据copyright动态生成handleList
		generateHandleList() {
			const copyright = app.globalData.store.$state.copyright;
			const diy = app.globalData.store.$state.diy;
			console.log(diy);
			this.version = copyright.version;
			var handleList = [];
			if (copyright.version == 1) {
				handleList = [{
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `tuwen`,
					vip: 0,
				}];
			} else {
				handleList.push({
					imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.writing.images,
					text: app.globalData.store.$state.diy.pattern_data.release.list.writing.title,
					url: `tuwen`,
					vip: 0,
				});
				//发布音频
				if (copyright.hair_audio_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.audio.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.audio.title,
						url: `yuyin`,
						vip: diy.user_vip.voice_member,
					});
				}
				//发布投票
				if (copyright.hair_vote_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.graffito.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.graffito.title,
						url: `toupiao`,
						vip: diy.user_vip.vote_member,
					});
				}
				//发布视频
				if (copyright.hair_video_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.video.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.video.title,
						url: `shipin`,
						vip: diy.user_vip.video_member,
					});
				}
				//发布活动
				if (copyright.hair_brisk_arbor == 1) {
					handleList.push({
						imageUrl: app.globalData.store.$state.diy.pattern_data.release.list.brisk.images,
						text: app.globalData.store.$state.diy.pattern_data.release.list.brisk.title,
						url: `huodong`,
						vip: diy.user_vip.brisk_member,
					});
				}
			}
			this.handleList = handleList;
		},
		switchClick(data, index) {
			console.log(data);
			console.log(index);
			console.log(this.activeIndex);
			if (index == this.activeIndex) {
				return
			}
			if (index === 2) {
				this.visible = !this.visible;

				if (this.activeIndex == 1) {
					uni.$emit('hideCanvas', this.visible)
				}
				return
			}
			if (data.home == 1) {
				uni.navigateTo({
					url: data.pagePath
				});
			} else {
				uni.switchTab({
					url: data.pagePath
				});
			}
		},
		handleClose() {
			this.startCloseAnimation();
		},
		get_user_info() {
			var b = app.globalData.api_root + 'User/get_user_info';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: function (res) {
					console.log('user_info', res);
					if (res.data.status == 'success') {
						that.user_info = res.data.info;
						//that.get_diy();
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						});
					}
				},
				fail: function () {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: function (res) { }
					});
				}
			});
		},
		get_diy() {
			var b = app.globalData.api_root + 'User/get_diy';
			var that = this;
			var e = app.globalData.getCache("userinfo");
			var params = new Object();
			params.uid = e.uid;
			params.token = e.token;
			params.openid = e.openid;
			http.POST(b, {
				params: params,
				success: function (res) {
					console.log(res);
					if (res.data.status) {
						return;
					} else {
						that.version = res.data.version;
						that.diy = res.data;
						that.add = res.data.pattern_data.release.list;
					}
				},
				fail: function () {
					uni.showModal({
						title: '提示',
						content: '网络繁忙，请稍候重试！',
						showCancel: false,
						success: function (res) { }
					});
				}
			});
		},
		handleClick(k) {
			const copyright = app.globalData.store.$state.copyright;
			const user_info = this.user_info;
			console.log(user_info);
			var checkLogin = app.globalData.checkPhoneLogin(1);
			if (checkLogin == 1) {
				this.check_user_login = true;
				return;
			}
			if (checkLogin == 2) {
				this.check_phone_show = true;
				return;
			}

			if (k == 'tuwen') {
				uni.navigateTo({
					url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=0&name=&gambit_name=&gambit_id=0'
				});
			}
			if (k == 'toupiao') {
				if (copyright['vote_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'yuyin') {
				if (copyright['voice_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'shipin') {
				if (copyright['video_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
			if (k == 'huodong') {
				if (copyright['brisk_member'] == 1) {
					if (user_info['is_vip'] == 1) {
						uni.navigateTo({
							url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
						});
					} else {
						uni.showToast({
							title: '此功能仅限VIP用户使用',
							icon: 'none',
							duration: 2000
						});
						return;
					}
				} else {
					uni.navigateTo({
						url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=0&name=&gambit_name=&gambit_id=0'
					});
				}
			}
		},
		// 刷新handleList的公共方法，供父组件调用
		refreshHandleList() {
			this.generateHandleList();
		},
		// 返回顶部方法
		goTop: function (e) {
			if (uni.pageScrollTo) {
				uni.pageScrollTo({
					selector: "#top_order",
					offsetTop: -100
				});
			} else {
				uni.showModal({
					title: '提示',
					content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.tqb-tabbar {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 100000;
}

.tqb-tabbar-body {
	border-radius: 80rpx;
	height: 130rpx;
	display: flex;
	background: linear-gradient(to right, #f5f0f7, #e8f0f6, #edf7f9);
	position: fixed;
	bottom: 40rpx;
	width: 91%;
	left: 0;
	right: 0;
	z-index: 1001;
	padding-top: 20rpx;
	margin: 0 auto;
	box-shadow: 0px 0px 22rpx 9rpx rgba(0, 0, 0, 0.1);

	.tqb-tabbar-item {
		flex: 1;
		display: flex;
		justify-content: center;
		text-align: center;

		.tabbar-item-body {}

		.tabbar-item-icon {
			display: flex;
			justify-content: center;
			margin-bottom: 10rpx;
		}

		.tab-bar-text {
			color: #999999;
			font-size: 22rpx;
			margin-top: 9rpx;
		}

		.active {
			color: #645AEE;
		}

		.add-icon {
			width: 88rpx;
			height: 88rpx;
			position: relative;
		}
	}
}

.mark {
	position: fixed;
	color: #333;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	font-size: 26rpx;
	z-index: 2000;
	text-align: center;

	.mark-title {
		position: relative;
		top: 80px;
		left: 35px;
		color: #484949;
		text-align: left;
		animation: slideInFromLeft 0.6s ease-out 0.2s backwards;

		.time-container {
			display: flex;
			align-items: baseline;
			margin-bottom: 20rpx;

			.time {
				display: flex;
				align-items: baseline;
				margin-right: 20rpx;

				.day {
					font-size: 72rpx;
					font-weight: bold;
					line-height: 1;
				}

				.month {
					font-size: 28rpx;
					margin-left: 8rpx;
					opacity: 0.8;
					font-weight: 500;
				}
			}

			.year {
				font-size: 24rpx;
				opacity: 0.6;
				font-weight: 300;
			}
		}



		.decoration {
			position: absolute;
			right: -20rpx;
			top: 50%;
			transform: translateY(-50%);
			display: flex;
			align-items: center;

			.dot {
				width: 12rpx;
				height: 12rpx;
				background: #484949;
				border-radius: 50%;
				opacity: 0.6;
			}

			.line {
				width: 60rpx;
				height: 2rpx;
				background: linear-gradient(to right, #484949, transparent);
				margin-left: 8rpx;
				opacity: 0.4;
			}
		}
	}
}

.mak-box {
	position: absolute;
	bottom: 100px;
	left: 0;
	right: 0;
	color: #484949;
	display: flex;
	flex-wrap: wrap;
	padding: 0 40rpx;
	gap: 30rpx;

	.box {
		width: 30%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		animation: example1 0.4s ease-out 0.4s backwards;

		.img-view {
			width: 58px;
			height: 58px;
			border-radius: 50%;
			margin-bottom: 10rpx;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;

			.vip-badge {
				position: absolute;
				top: -8rpx;
				right: -8rpx;
				background: linear-gradient(135deg, #FFD700, #FFA500);
				color: #fff;
				font-size: 20rpx;
				font-weight: bold;
				padding: 6rpx 10rpx;
				border-radius: 22rpx;
				z-index: 10;
				box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.3);
				border: 2rpx solid #fff;
				line-height: 1;
				transform: scale(1);
			}
		}

		image {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
		}

		text {
			font-size: 28rpx;
		}
	}
}

.close {
	margin-top: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	animation: example1 0.5s ease-out 0.5s backwards;

	image {
		width: 40px;
		height: 40px;
	}
}

@keyframes example1 {
	0% {
		transform: translateY(-60px);
		opacity: 0;
	}

	50% {
		transform: translateY(0);
		opacity: 1;
	}

	100% {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes slideInFromLeft {
	0% {
		transform: translateX(-80px);
		opacity: 0;
	}

	60% {
		transform: translateX(10px);
		opacity: 0.8;
	}

	100% {
		transform: translateX(0);
		opacity: 1;
	}
}

.content_tab {
	.label {
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 8rpx;
		line-height: 1.2;
	}

	.subtitle {
		font-size: 24rpx;
		opacity: 0.7;
		font-weight: 300;
	}
}

/* mark 元素的过渡动画 */
.mark-fade-enter-active {
	transition: all 0.5s ease-out;
}

.mark-fade-leave-active {
	transition: all 0.4s ease-in;
}

.mark-fade-enter {
	opacity: 0;
	transform: scale(0.8);
}

.mark-fade-leave {
	opacity: 1;
	transform: scale(1);
}

.mark-fade-leave-to {
	opacity: 0;
	transform: scale(0.9);
}

/* 确保子元素动画在进入和退出时正确工作 */
.mark-show .mark-title,
.mark-show .box,
.mark-show .close {
	animation-play-state: running;
}

.mark-hide .mark-title,
.mark-hide .box,
.mark-hide .close {
	animation: none;
}

/* 进入和退出动画 */
.mark-show {
	/* 进入时直接显示，依靠子元素动画创造效果 */
	opacity: 1;
}

.mark-hide {
	animation: markFadeOut 0.2s ease-in forwards;
}

@keyframes markFadeOut {
	0% {
		opacity: 1;
		transform: scale(1);
	}

	100% {
		opacity: 0;
		transform: scale(0.9);
	}
}
</style>
