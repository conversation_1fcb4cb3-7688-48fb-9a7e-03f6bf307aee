<template>
    <view>
        <view v-if="info_home.length > 0" style="width: 100%">
            <swiper class="round-dot" :indicator-dots="true"
                :style="'height:' + (info_home[0].length > 4 ? 400 : 220) + 'rpx;'" indicator-active-color="#696969"
                indicator-color="rgba(150, 150, 150, 0.8)">
                <block v-for="(one, e_index) in info_home" :key="e_index">
                    <swiper-item>
                        <view class="grid col-4 text-center" style="padding-top: 10px; width: 100%">
                            <view @tap="set_one" :data-type="n_item.practice_type" :data-path="n_item.wx_app_url"
                                :data-url="n_item.url" :data-key="e_index" :data-index="n_index" style="margin: 5px 0px"
                                v-for="(n_item, n_index) in one" :key="n_index">
                                <image :lazy-load="true" :src="n_item.playbill_url" style="width: 90rpx; height: 90rpx">
                                </image>

                                <view v-if="n_item.playbill_name" class="botton_text">
                                    {{ n_item.playbill_name }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <view v-if="put_top_list.length > 0 && put_top_info.display_switch == 1">
            <view v-if="put_top_info.style_type == 0" @tap="top_url" class="text-center" style="position: relative">
                <view class="text-bold"
                    style="color: #ffffff; position: absolute; z-index: 10; font-size: 36rpx; width: 94rpx; left: 56rpx; top: 24rpx; letter-spacing: 6rpx">
                    {{ put_top_info.custom_title }}
                </view>
                <view
                    style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 30rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 90rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'">
                                </image>
                                <text
                                    style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 20rpx">
                                围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix"
                    src="/static/yl_welore/style/icon/sign_top_bac.png"></image>
            </view>
            <view v-if="put_top_info.style_type == 1" @tap="top_url" class="text-center" style="position: relative">
                <view
                    style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 0rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical
                        style="height: 120rpx; padding-top: 15rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'">
                                </image>
                                <text
                                    style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 25rpx">
                                围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix"
                    src="/static/yl_welore/style/icon/sign_top_bac1.png"></image>
            </view>
        </view>

        <view v-if="copyright.home_my_tory_arbor == 1" class="my-realm-container">
            <!-- 头部标题区域 -->
            <view class="realm-header" :style="'margin-top: ' + (info_home.length == 0 ? 20 : 0) + 'px;'" @click="toggleMyRealm">
                <view class="my-realm-title">
                    <view class="title-left">
                        <text class="cicon-user my-icon"></text>
                        <text>我的{{ $state.diy.landgrave }}</text>
                    </view>
                    <text class="cicon-angle collapse-icon" :class="{ 'collapsed': myRealmCollapsed }"></text>
                </view>
                <view class="realm-more-btn" @click.stop="get_all_qq" v-if="!myRealmCollapsed">
                    <text class="more-text">全部</text>
                    <text class="cicon-angle more-icon"></text>
                </view>
            </view>

            <!-- 滚动卡片区域 -->
            <view class="scroll-container-wrapper" :class="{ 'collapsed': myRealmCollapsed }">
                <scroll-view :scroll-x="true" @scrolltolower="nex_my_qq" class="realm-scroll-container">
                <view :data-id="item.id" @tap="this_url" class="realm-card my-card" v-for="(item, index) in my_qq_list"
                    :key="index">
                    <!-- 头像容器 -->
                    <view class="realm-avatar-container">
                        <view class="realm-avatar" :style="'background-image:url(' + item.realm_icon + ');'"></view>
                    </view>

                    <!-- 名称文字 -->
                    <view class="realm-name">
                        <text class="text-cut">{{ item.realm_name }}</text>
                    </view>
                    <!-- 成员数量 -->
                    <view class="member-count">
                        <view class="member-text">
                            <text class="cicon-accounts"></text>
                            <text>{{ item.concern }}</text>
                        </view>
                    </view>
                </view>

                <!-- 空状态 -->
                <view v-if="my_qq_list.length == 0" @tap="get_all_qq" class="realm-empty-state">
                    <view class="empty-content">
                        <text class="cicon-discover empty-icon"></text>
                        <text class="empty-text">查看更多{{ $state.diy.landgrave }}</text>
                    </view>
                </view>
                </scroll-view>
            </view>
        </view>
        <!-- 附近的圈子 -->
        <view class="nearby-realm-container" v-if="near && copyright.nearby_is_home_show==1">
            <!-- 头部标题区域 -->
            <view class="realm-header" @tap="toggleNearbyRealm">
                <view class="nearby-realm-title">
                    <view class="title-left">
                        <text class="cicon-location nearby-icon"></text>
                        <text>附近的{{ $state.diy.landgrave }}</text>
                    </view>
                    <text class="cicon-angle collapse-icon" :class="{ 'collapsed': nearbyRealmCollapsed }"></text>
                </view>
                <view class="realm-more-btn" @tap.stop="get_nearby_realms" v-if="!nearbyRealmCollapsed">
                    <text class="more-text">更多</text>
                    <text class="cicon-angle more-icon"></text>
                </view>
            </view>

            <!-- 滚动卡片区域 -->
            <view class="scroll-container-wrapper" :class="{ 'collapsed': nearbyRealmCollapsed }">
                <scroll-view :scroll-x="true" class="realm-scroll-container">
                <view :data-id="item.id" @tap="this_url" class="realm-card nearby-card"
                    v-for="(item, index) in near_list" :key="index">
                    <!-- 头像容器 -->
                    <view class="realm-avatar-container">
                        <view class="realm-avatar" :style="'background-image:url(' + item.realm_icon + ');'"></view>
                        <!-- 距离标签 -->
                        <view class="distance-tag">{{ item.distance_text }}</view>
                    </view>

                    <!-- 名称文字 -->
                    <view class="realm-name">
                        <text class="text-cut">{{ item.realm_name }}</text>
                    </view>

                    <!-- 成员数量 -->
                    <view class="member-count">
                        <view class="member-text">
                            <text class="cicon-accounts"></text>
                            <text>{{ item.concern }}</text>
                        </view>
                    </view>
                </view>

                <!-- 空状态 -->
                <view v-if="near_list.length == 0" @tap="get_nearby_realms" class="realm-empty-state">
                    <view class="empty-content">
                        <text class="cicon-location-on empty-icon"></text>
                        <text class="empty-text">发现附近{{ $state.diy.landgrave }}</text>
                    </view>
                </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    data() {
        return {
            myRealmCollapsed: false, // 我的圈子折叠状态，默认关闭
            nearbyRealmCollapsed: false // 附近圈子折叠状态，默认关闭
        };
    },
    computed: {
        info_home() {
            return (this.data && this.data.info_home) || [];
        },
        put_top_list() {
            return (this.data && this.data.put_top_list) || [];
        },
        put_top_info() {
            return (this.data && this.data.put_top_info) || {};
        },
        http_root() {
            return this.data && this.data.http_root;
        },
        copyright() {
            return (this.data && this.data.copyright) || {};
        },

        my_qq_list() {
            return (this.data && this.data.my_qq_list) || [];
        },

        near_list() {
            return (this.data && this.data.near_list) || [];
        },
        near() {
            return this.data && this.data.near;
        }

    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        set_one(e) {
            this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_home_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        },
        get_nearby_realms(e) {
            this.$emit('get_nearby_realms', e);
        },
        toggleMyRealm() {
            this.myRealmCollapsed = !this.myRealmCollapsed;
        },
        toggleNearbyRealm() {
            this.nearbyRealmCollapsed = !this.nearbyRealmCollapsed;
        }
    }
};
</script>
<style scoped>
/* 我的圈子样式 - 现代设计 */
.my-realm-container {
    margin: 24rpx 20rpx 32rpx 20rpx;
    background: linear-gradient(145deg, #fafafa 0%, #ffffff 30%, #f8fafc 100%);
    border-radius: 24rpx;
    border: 1rpx solid #e2e8f0;
    box-shadow: 0 12rpx 40rpx rgba(15, 23, 42, 0.08), 0 4rpx 16rpx rgba(15, 23, 42, 0.04);
    overflow: hidden;
    position: relative;
}

/* 我的圈子顶部装饰条 - 紫色系 */
.my-realm-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 25%, #c084fc 50%, #e879f9 75%, #f0abfc 100%);
    border-radius: 24rpx 24rpx 0 0;
    z-index: 1;
    width: 97%;
    margin: 0 auto;
}

/* 我的圈子背景装饰元素 */
.my-realm-container::after {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 120rpx;
    height: 120rpx;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

/* 我的圈子标题样式 */
.my-realm-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 34rpx;
    color: #1e293b;
    letter-spacing: 0.5rpx;
    position: relative;
    font-weight: 700;
    z-index: 2;
    width: 100%;
}

.title-left {
    display: flex;
    align-items: center;
}

.my-icon {
    font-size: 40rpx;
    color: #8b5cf6;
    margin-right: 12rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(139, 92, 246, 0.2));
}

/* 折叠图标样式 */
.collapse-icon {
    font-size: 24rpx;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: rotate(90deg);
    opacity: 0;
}

.collapse-icon.collapsed {
    transform: rotate(-90deg);
    color: #8b5cf6;
    opacity: 1;
}

/* 我的圈子标题点击效果 */
.my-realm-container .realm-header {
    cursor: pointer;
    transition: all 0.2s ease;
}

.my-realm-container .realm-header:active {
    background: rgba(139, 92, 246, 0.05);
}

/* 附近圈子标题点击效果 */
.nearby-realm-container .realm-header {
    cursor: pointer;
    transition: all 0.2s ease;
}

.nearby-realm-container .realm-header:active {
    background: rgba(59, 130, 246, 0.05);
}

/* 我的圈子卡片样式 */
.my-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1rpx solid #e2e8f0;
    box-shadow: 0 8rpx 25rpx rgba(15, 23, 42, 0.08), 0 3rpx 10rpx rgba(15, 23, 42, 0.04);
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 我的圈子头像样式 */
.my-card .realm-avatar {
    width: 100rpx;
    height: 100rpx;
    box-shadow: 0 8rpx 24rpx rgba(15, 23, 42, 0.12), 0 4rpx 8rpx rgba(15, 23, 42, 0.08);
    position: relative;
    border-radius: 20rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 我的圈子文字样式 */
.my-card .realm-name .text-cut {
    color: #1e293b;
    font-size: 28rpx;
    text-shadow: 0 1rpx 2rpx rgba(15, 23, 42, 0.1);
}

/* 我的圈子成员数量样式 - 紫色系 */
.my-card .member-count {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12rpx;
    font-size: 20rpx;
    position: relative;
    background: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
    transition: all 0.3s ease;
}

.my-card .member-text {
    font-weight: 600;
    color: #8b5cf6;
    letter-spacing: 0.3rpx;
    position: relative;
    padding: 4rpx 12rpx;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.08) 0%, rgba(139, 92, 246, 0.04) 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(139, 92, 246, 0.15);
    font-size: 20rpx;
}



/* 我的圈子交互效果 */
.my-card:active {
    transform: translateY(-6rpx) scale(0.97);
    box-shadow: 0 25rpx 50rpx rgba(15, 23, 42, 0.15), 0 10rpx 20rpx rgba(15, 23, 42, 0.1);
}

.my-card:active .realm-avatar {
    transform: scale(1.08);
    box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.2), 0 6rpx 12rpx rgba(15, 23, 42, 0.15);
}

.my-card:active .member-count {
    transform: scale(1.05);
}

.my-card:active .member-text {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.12) 0%, rgba(139, 92, 246, 0.08) 100%);
    border-color: rgba(139, 92, 246, 0.25);
    color: #7c3aed;
}

/* 我的圈子专用的更多按钮样式 */
.my-realm-container .realm-more-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1rpx solid #c4b5fd;
    box-shadow: 0 6rpx 16rpx rgba(139, 92, 246, 0.25), 0 2rpx 4rpx rgba(139, 92, 246, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    min-width: fit-content;
    position: relative;
    z-index: 3;
}

.my-realm-container .realm-more-btn:active {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: scale(0.95) translateY(1rpx);
    box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.3), 0 1rpx 2rpx rgba(139, 92, 246, 0.2);
}

.my-realm-container .more-text {
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5rpx;
    white-space: nowrap;
    font-size: 24rpx;
    margin-right: 4rpx;
}

.my-realm-container .more-icon {
    color: #ffffff;
    filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}



/* 头部标题区域 */
.realm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 24rpx 16rpx 24rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f5f5f5;
    position: relative;
}

.realm-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e40af;
    letter-spacing: 0.5rpx;
}

.realm-more-btn {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    transition: all 0.2s ease;
}

.realm-more-btn:active {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: scale(0.98);
}

.more-text {
    font-size: 24rpx;
    color: #ffffff;
    margin-right: 4rpx;
    font-weight: 500;
}

.more-icon {
    font-size: 22rpx;
    color: #ffffff;
    transition: all 0.2s ease;
}

/* 滚动容器包装器 */
.scroll-container-wrapper {
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-height: 1000rpx;
    opacity: 1;
}

.scroll-container-wrapper.collapsed {
    max-height: 0;
    opacity: 0;
}

/* 滚动容器 */
.realm-scroll-container {
    white-space: nowrap;
    width: 100%;
    padding: 30rpx;
    background: #ffffff;
    position: relative;
}

/* 内部小卡片样式 */
.realm-card {
    display: inline-block;
    width: 200rpx;
    margin: 0 8rpx;
    padding: 25rpx 12rpx;
    background: #ffffff;
    border-radius: 12rpx;
    border: 1rpx solid #f1f5f9;
    box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.06);
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
    overflow: hidden;
}

.realm-card:active {
    transform: translateY(-2rpx) scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.15);
}



/* 头像容器 */
.realm-avatar-container {
    margin-bottom: 20rpx;
    position: relative;
    display: flex;
    justify-content: center;
}

/* 附近圈子的头像容器需要更多底部空间 */
.nearby-card .realm-avatar-container {
    margin-bottom: 24rpx;
}

.realm-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 12rpx;
    background-color: #f8f9fa;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    position: relative;
}





.realm-card:active .realm-avatar {
    transform: scale(1.02);
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 名称文字 */
.realm-name {
    font-size: 26rpx;
    color: #333333;
    line-height: 1.3;
    font-weight: 500;
    padding: 0 4rpx;
}

/* 为名称添加对应的颜色 */
.realm-card:nth-child(4n+1) .realm-name .text-cut {
    color: #ef4444;
}

.realm-card:nth-child(4n+2) .realm-name .text-cut {
    color: #10b981;
}

.realm-card:nth-child(4n+3) .realm-name .text-cut {
    color: #3b82f6;
}

.realm-card:nth-child(4n+4) .realm-name .text-cut {
    color: #f59e0b;
}

.realm-name .text-cut {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 空状态样式 */
.realm-empty-state {
    display: inline-block;
    width: 100%;
    padding: 20rpx;
    text-align: center;
}

.empty-content {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 50rpx;
    background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
    border-radius: 20rpx;
    border: 2rpx dashed #3b82f6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.empty-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

.empty-content:active {
    transform: scale(0.98);
    background: linear-gradient(135deg, #eff6ff 0%, #f8faff 100%);
    border-color: #2563eb;
}

.empty-icon {
    font-size: 48rpx;
    color: #3b82f6;
    margin-bottom: 16rpx;
    animation: bounce 2s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

.empty-text {
    font-size: 28rpx;
    color: #1e40af;
    font-weight: 600;
    position: relative;
    z-index: 1;
}



/* 动画效果 */
@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }

    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-8rpx);
    }

    60% {
        transform: translateY(-4rpx);
    }
}

/* 附近圈子样式 - 全新现代设计 */
.nearby-realm-container {
    margin: 24rpx 20rpx 32rpx 20rpx;
    background: linear-gradient(145deg, #fafafa 0%, #ffffff 30%, #f8fafc 100%);
    border-radius: 24rpx;
    border: 1rpx solid #e2e8f0;
    box-shadow: 0 12rpx 40rpx rgba(15, 23, 42, 0.08), 0 4rpx 16rpx rgba(15, 23, 42, 0.04);
    overflow: hidden;
    position: relative;
}

/* 顶部装饰条 */
.nearby-realm-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #3b82f6 0%, #06b6d4 25%, #10b981 50%, #f59e0b 75%, #ef4444 100%);
    border-radius: 24rpx 24rpx 0 0;
    z-index: 1;
    width: 97%;
    margin: 0 auto;
}

/* 背景装饰元素 */
.nearby-realm-container::after {
    content: '';
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 120rpx;
    height: 120rpx;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 0;
}

.nearby-realm-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 34rpx;
    color: #1e293b;
    letter-spacing: 0.5rpx;
    position: relative;
    z-index: 2;
    font-weight: 700;
    width: 100%;
}

.nearby-icon {
    font-size: 40rpx;
    color: #3b82f6;
    margin-right: 12rpx;
    filter: drop-shadow(0 2rpx 4rpx rgba(59, 130, 246, 0.2));
}

/* 附近圈子卡片样式 - 现代卡片设计 */
.nearby-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}


/* 卡片悬停效果 */
.nearby-card:hover {
    transform: translateY(-8rpx);
    box-shadow: 0 20rpx 40rpx rgba(15, 23, 42, 0.12), 0 8rpx 16rpx rgba(15, 23, 42, 0.08);
}

.nearby-card .realm-avatar {
    width: 100rpx;
    height: 100rpx;
    box-shadow: 0 8rpx 24rpx rgba(15, 23, 42, 0.12), 0 4rpx 8rpx rgba(15, 23, 42, 0.08);
    position: relative;
    border-radius: 20rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nearby-card .realm-name .text-cut {
    color: #1e293b;
    font-size: 28rpx;
    text-shadow: 0 1rpx 2rpx rgba(15, 23, 42, 0.1);
}

/* 距离标签 - 精致小标签 */
.distance-tag {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    font-size: 18rpx;
    padding: 4rpx 8rpx;
    border-radius: 12rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.25), 0 1rpx 3rpx rgba(239, 68, 68, 0.15);
    border: 2rpx solid #ffffff;
    z-index: 3;
    animation: pulse-distance 3s ease-in-out infinite;
    letter-spacing: 0.3rpx;
    min-width: 32rpx;
    text-align: center;
}

/* 附近圈子卡片交互效果 */
.nearby-card:active {
    transform: translateY(-6rpx) scale(0.97);
    box-shadow: 0 25rpx 50rpx rgba(15, 23, 42, 0.15), 0 10rpx 20rpx rgba(15, 23, 42, 0.1);
}

.nearby-card:active .realm-avatar {
    transform: scale(1.08);
    box-shadow: 0 12rpx 32rpx rgba(15, 23, 42, 0.2), 0 6rpx 12rpx rgba(15, 23, 42, 0.15);
}

.nearby-card:active .distance-tag {
    transform: scale(1.1);
    box-shadow: 0 6rpx 16rpx rgba(239, 68, 68, 0.35), 0 2rpx 6rpx rgba(239, 68, 68, 0.25);
}

.nearby-card:active .member-count {
    transform: scale(1.05);
}

.nearby-card:active .member-text {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.08) 100%);
    border-color: rgba(59, 130, 246, 0.25);
    color: #2563eb;
}

/* 距离标签脉冲动画 */
@keyframes pulse-distance {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.25), 0 1rpx 3rpx rgba(239, 68, 68, 0.15);
    }

    50% {
        transform: scale(1.05);
        box-shadow: 0 6rpx 16rpx rgba(239, 68, 68, 0.35), 0 2rpx 6rpx rgba(239, 68, 68, 0.25);
    }
}

/* 头像光晕动画 */
@keyframes avatar-glow {

    0%,
    100% {
        opacity: 0.2;
        filter: blur(8rpx);
    }

    50% {
        opacity: 0.4;
        filter: blur(12rpx);
    }
}

.nearby-card .realm-avatar::after {
    animation: avatar-glow 4s ease-in-out infinite;
}

/* 附近圈子专用的更多按钮样式 */
.nearby-realm-container .realm-more-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: 1rpx solid #93c5fd;
    box-shadow: 0 6rpx 16rpx rgba(59, 130, 246, 0.25), 0 2rpx 4rpx rgba(59, 130, 246, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    min-width: fit-content;
    position: relative;
    z-index: 3;
}

.nearby-realm-container .realm-more-btn:active {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: scale(0.95) translateY(1rpx);
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3), 0 1rpx 2rpx rgba(59, 130, 246, 0.2);
}

.nearby-realm-container .more-text {
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5rpx;
    white-space: nowrap;
    font-size: 24rpx;
    margin-right: 4rpx;
}

.nearby-realm-container .more-icon {
    color: #ffffff;
    filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
}

/* 成员数量 - 全新设计 */
.member-count {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12rpx;
    font-size: 20rpx;
    position: relative;
    background: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
    transition: all 0.3s ease;
}

.member-icon {
    display: none;
}

.member-text {
    font-weight: 600;
    color: #64748b;
    letter-spacing: 0.3rpx;
    position: relative;
    padding: 4rpx 12rpx;
    background: linear-gradient(135deg, rgba(100, 116, 139, 0.08) 0%, rgba(100, 116, 139, 0.04) 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(100, 116, 139, 0.12);
    font-size: 20rpx;
}



/* 附近圈子成员数量样式 - 蓝色系 */
.nearby-card .member-count {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12rpx;
    font-size: 20rpx;
    position: relative;
    background: transparent;
    padding: 0;
    border: none;
    box-shadow: none;
    transition: all 0.3s ease;
}

.nearby-card .member-icon {
    display: none;
}

.nearby-card .member-text {
    font-weight: 600;
    color: #3b82f6;
    letter-spacing: 0.3rpx;
    position: relative;
    padding: 4rpx 12rpx;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(59, 130, 246, 0.15);
    font-size: 20rpx;
}
</style>
