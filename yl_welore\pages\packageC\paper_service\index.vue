<template>
	<view>
		<cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
			<view slot="backText">返回</view>
			<view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
		</cu-custom>
		<view v-if="TabCur == 0" style="background-color: #ffffff">
			<block v-for="(item, dataListindex) in new_list">
				<view style="background-color: #fff; overflow: hidden" class="weui-cells weui-cells_after-title">
					<view style="padding-bottom: 10px">
						<!-- 头像 -->
						<view class="cu-list menu-avatar">
							<view class="cu-item">
								<view @tap="home_url(1, item.user_id)" class="cu-avatar round lg"
									:style="'background-image:url(' + item.user_head_sculpture + ');'">
									<view
										:class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')">
									</view>
								</view>
								<view class="content flex-sub">
									<view class="align-center">
										<view>{{ item.user_nick_name }}</view>
										<image class="now_level" mode="heightFix" :src="item.level"
											style="height: 35rpx; vertical-align: middle; margin-left: 5px"></image>
										<image v-if="item.user_vip == 1"
											:src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
											style="width: 20px; height: 20px; vertical-align: bottom; margin-left: 3px">
										</image>
										<image v-if="item.wear_merit" :src="item.wear_merit"
											style="width: 20px; height: 20px; vertical-align: bottom; margin-left: 3px">
										</image>
									</view>
									<view class="text-gray text-sm flex justify-between">
										<text style="font-size: 13px; color: #888888">{{ item.adapter_time }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 头像 -->
						<!-- 内容 -->
						<navigator
							:url="'/yl_welore/pages/packageA/article/index?id=' + item.id + '&type=' + item.study_type"
							hover-class="none">
							<view>
								<view class="weui-cell" style="padding: 0rpx 10px 10px 20px">
									<view class="weui-cell__hd text_num"
										:style="'font-size:16px;position: relative;margin-right: 10px;color:' + item.study_title_color + ';'">
										<rich-text
											:nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
									</view>
								</view>
								<view v-if="item.study_type == 0 || item.study_type == 4 || item.study_type == 5"
									style="overflow: hidden">
									<!-- 1 -->
									<view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''"
										v-for="(img, img_index) in item.image_part" :key="img_index">
										<image :lazy-load="true" :src="img" style="width: 100%; height: 190px"
											mode="aspectFill"></image>
									</view>
									<!-- 1 -->
									<!-- 2 -->
									<view
										:style="'width:31%;float:left;text-align:center;padding-left:' + (img_index == 0 ? 18 : 8) + 'px;'"
										v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part"
										:key="img_index">
										<image :lazy-load="true" v-if="img_index == 0" :src="img"
											style="height: 100px; width: 100%" mode="aspectFill"></image>

										<image :lazy-load="true" v-if="img_index == 1" :src="img"
											style="height: 100px; width: 100%" mode="aspectFill"></image>
									</view>
									<!-- 2 -->
									<!-- 3 -->
									<block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part"
										:key="img_index">
										<view style="width: 31%; float: left; text-align: center; padding-left: 8px"
											v-if="img_index == 0">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>

										<view style="width: 31%; float: left; text-align: center; padding-left: 5px"
											v-if="img_index == 1">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>

										<view style="width: 31%; float: left; text-align: center; padding-left: 5px"
											v-if="img_index == 2">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>
									</block>
									<!-- 3 -->
									<view v-if="item.study_type == 4 || item.study_type == 5" class="shadow-warp"
										style="margin: 15px; background-color: #f8f8f8">
										<view style="padding: 15px; text-align: center">
											<view class="text_num" style="font-size: 15px; font-weight: 600">
												<text v-if="item.study_type == 4">（单选）</text>
												<text v-if="item.study_type == 5">（多选）</text>
												<rich-text v-if="item.study_title != ''"
													:nodes="item.study_title"></rich-text>
											</view>
											<view style="height: 10px"></view>
											<view style="position: relative" v-if="vo_index < 3"
												v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
												<view
													style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
													class="text_num bg-white">
													<view class="text-cut"
														style="z-index: 3; position: relative; width: 70%; margin: 0 auto">
														{{ vo_item.ballot_name }}
													</view>

													<text v-if="voi_item == vo_item.id"
														:style="'position: absolute;right: ' + (item.is_vo_check > 0 ? 90 : 7) + '%;z-index:3;top: 0;'"
														class="cuIcon-check lg text-green"
														v-for="(voi_item, index) in item.vo_id" :key="index"></text>
													<text v-if="item.is_vo_check > 0"
														style="z-index: 3; position: absolute; right: 40rpx; top: 0">{{
                                                        vo_item.voters }}</text>
												</view>

												<view v-if="item.is_vo_check > 0" class="cu-progress radius sm" style="
                                                        position: absolute;
                                                        z-index: 1;
                                                        left: 0;
                                                        right: 0;
                                                        top: 0;
                                                        width: 95%;
                                                        height: 40px;
                                                        margin: 0 auto;
                                                        background-color: #ffffff;
                                                    ">
													<view
														:style="'width:' + vo_item.ratio + '%;background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);'">
													</view>
												</view>
											</view>

											<view @tap.stop.prevent="home_url(3, item.id, item.study_type)"
												v-if="item.vo.length > 3"
												style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
												class="text_num bg-white">
												查看全部选项
												<text class="cuIcon-right lg text-gray"></text>
											</view>
										</view>
										<view
											v-if="item.vote_deadline != '' && item.vote_deadline != 0 && item.vote_deadline != -1"
											style="font-weight: 300; margin-left: 46rpx">
											截止时间：{{ item.vote_deadline }}
										</view>
										<view v-if="item.vote_deadline == -1"
											style="font-weight: 300; margin-left: 46rpx">
											投票已截止</view>
										<view class="flex align-end" style="padding: 10px 0px">
											<view class="flex-sub">
												<view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ item.vo_count
                                                    }}</view>
											</view>
										</view>
									</view>
								</view>

								<view class="weui-cell" v-if="item.study_type == 1">
									<view class="cu-chat">
										<view class="cu-item" style="padding: 30rpx 30rpx 20rpx 70rpx">
											<view @tap.stop.prevent="home_url(3, item.id, 1)" class="cu-avatar radius"
												:style="'background-image:url(' + item.user_head_sculpture + ');'">
											</view>
											<view class="main" @tap="togglePlay(item, dataListindex)">
												<view class="content shadow bg-green"
													style="width: 90px; position: relative">
													<text
														style="-moz-transform: rotate(90deg); -webkit-transform: rotate(90deg); font-size: 25px"
														v-if="!item.is_voice" class="cuIcon-wifi lg text-white"></text>
													<image v-if="item.is_voice"
														src="/static/yl_welore/style/icon/home_yuyim1.gif"
														style="width: 50rpx; height: 50rpx" mode="aspectFill"></image>
													<text style="margin-left: 5px">{{ item.study_voice_time == 0 ? 1 :
                                                        item.study_voice_time }}</text>
												</view>
											</view>
										</view>
									</view>
								</view>

								<view style="overflow: hidden" v-if="item.study_type == 1">
									<!-- 1 -->
									<view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''"
										v-for="(img, img_index) in item.image_part" :key="img_index">
										<image :lazy-load="true" :src="img" style="width: 100%; height: 190px"
											mode="aspectFill"></image>
									</view>
									<!-- 1 -->
									<!-- 2 -->
									<view
										:style="'width:31%;float:left;text-align:center;padding-left:' + (img_index == 0 ? 18 : 8) + 'px;'"
										v-if="item.image_part.length == 2" v-for="(img, img_index) in item.image_part"
										:key="img_index">
										<image :lazy-load="true" v-if="img_index == 0" :src="img"
											style="height: 100px; width: 100%" mode="aspectFill"></image>

										<image :lazy-load="true" v-if="img_index == 1" :src="img"
											style="height: 100px; width: 100%" mode="aspectFill"></image>
									</view>
									<!-- 2 -->
									<!-- 3 -->
									<block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part"
										:key="img_index">
										<view style="width: 31%; float: left; text-align: center; padding-left: 8px"
											v-if="img_index == 0">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>

										<view style="width: 31%; float: left; text-align: center; padding-left: 5px"
											v-if="img_index == 1">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>

										<view style="width: 31%; float: left; text-align: center; padding-left: 5px"
											v-if="img_index == 2">
											<image :lazy-load="true" :src="img" style="width: 100%; height: 100px"
												mode="aspectFill"></image>
										</view>
									</block>
									<!-- 3 -->
								</view>

								<view v-if="item.study_type == 2">
									<view class="weui-cell"
										style="width: 91%; margin: 0 auto; text-align: center; position: relative">
										<image v-if="item.image_part.length > 0" :src="item.image_part[0]"
											mode="aspectFill" style="height: 190px; margin: 0 auto"></image>
										<view v-if="!item.image_part || item.image_part.length == 0"
											class="bg-black padding radius text-center shadow-blur" style="
                                                position: relative;
                                                margin: 0 auto;
                                                width: 80%;
                                                height: 180px;
                                                z-index: 100;
                                                overflow: hidden;
                                                border-radius: 5px;
                                                font-size: 16px;
                                            ">
											<text class="cuIcon-videofill lg text-white"
												style="font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%; z-index: 1"></text>
										</view>
										<text class="cuIcon-videofill lg text-white"
											style="font-size: 40px; position: absolute; text-align: center; left: 44%; top: 37%; z-index: 1"></text>
									</view>
								</view>
							</view>
						</navigator>
						<!-- 内容 -->
						<view style="clear: both; height: 0"></view>
						<view class="" style="height: 40px; padding-top: 10px">
							<navigator :url="'/yl_welore/pages/packageA/circle_info/index?id=' + item.tory_id"
								hover-class="none">
								<view
									style="float: left; margin-left: 20px; font-size: 14px; padding-top: 6px; font-weight: 500; color: #3399ff"
									class="weui-flex__item">
									{{ item.realm_name }}
								</view>
							</navigator>
							<view style="float: right; margin-right: 15px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 1)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #33cc33; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核通过
								</button>
							</view>
							<view style="float: right; margin-right: 25px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 2)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #ff3333; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核拒绝
								</button>
							</view>
						</view>
					</view>
				</view>

				<view style="width: 93%; height: 1px; background-color: #f2f2f2; margin: 0 auto"></view>
			</block>
		</view>
		<view v-if="TabCur == 1" style="background-color: #ffffff">
			<block v-for="(item, dataListindex) in hui_list" :key="dataListindex">
				<view style="background-color: #fff; overflow: hidden" class="weui-cells weui-cells_after-title">
					<view style="padding-bottom: 10px">
						<!-- 头像 -->
						<view class="cu-list menu-avatar">
							<view class="cu-item">
								<view class="cu-avatar round lg"
									:style="'background-image:url(' + item.user_head_sculpture + ');'">
									<view
										:class="'cu-tag badge ' + (item.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')">
									</view>
								</view>
								<view class="content flex-sub">
									<view class="align-center">
										<view>{{ item.user_nick_name }}</view>
										<image class="now_level" :src="item.level" mode="heightFix"
											style="height: 26rpx; vertical-align: middle; margin-left: 5px"></image>
										<image v-if="item.user_vip == 1"
											:src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
											style="width: 20px; height: 20px; vertical-align: bottom; margin-left: 3px">
										</image>
									</view>
									<view class="text-gray text-sm flex justify-between">
										<text style="font-size: 13px; color: #888888">{{ item.apter_time }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 头像 -->
						<!-- 内容 -->
						<navigator
							:url="'/yl_welore/pages/packageA/article/index?id=' + item.paper_id + '&type=' + item.study_type"
							hover-class="none">
							<view>
								<view class="weui-cell" style="padding: 0rpx 10px 10px 20px">
									<view class="weui-cell__hd"
										style="font-size: 16px; position: relative; margin-right: 10px">
										<rich-text :nodes="item.reply_content"></rich-text>
									</view>
								</view>
								<view v-if="item.reply_type == 0" style="overflow: hidden">
									<!-- 1 -->
									<view style="padding: 0px 15px" v-if="item.image_part != ''">
										<image :lazy-load="true" :src="item.image_part"
											style="width: 100%; height: 190px" mode="aspectFill"></image>
									</view>
									<!-- 1 -->
								</view>
							</view>
						</navigator>
						<view class="weui-cell" v-if="item.reply_type == 1">
							<view class="audiosBox">
								<view class="audioOpen" @tap="togglePlay(item, dataListindex)" v-if="!item.is_voice">
									<text style="color: #4c9dee; font-size: 25px" class="cicon-play-arrow"></text>
								</view>
								<view class="audioOpen" @tap="togglePlay(item, dataListindex)" v-if="item.is_voice">
									<text style="color: #4c9dee; font-size: 26px" class="cicon-pause"></text>
								</view>
								<view class="slid">
									<slider @change="sliderChange($event, dataListindex)" block-size="12px" step="1"
										:value="item.offset" :max="item.max" selected-color="#4c9dee" />
									<navigator
										:url="'/yl_welore/pages/packageA/article/index?id=' + item.id + '&type=1'"
										hover-class="none">
										<view style="margin-top: 3px">
											<text class="times">{{ item.starttime || '00:00' }}</text>
											<text class="times">{{ item.reply_voice_time }}</text>
										</view>
									</navigator>
								</view>
							</view>
						</view>
						<!-- 内容 -->
						<view style="clear: both; height: 0"></view>
						<view class="" style="height: 40px; padding-top: 10px">
							<navigator
								:url="'/yl_welore/pages/packageA/article/index?id=' + item.paper_id + '&type=' + item.study_type"
								hover-class="none">
								<view
									style="float: left; margin-left: 20px; font-size: 14px; padding-top: 6px; font-weight: 500; color: #3399ff"
									class="weui-flex__item">
									帖子详情
								</view>
							</navigator>
							<view style="float: right; margin-right: 15px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 1)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #33cc33; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核通过
								</button>
							</view>
							<view style="float: right; margin-right: 25px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 2)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #ff3333; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核拒绝
								</button>
							</view>
						</view>
					</view>
				</view>

				<view style="width: 93%; height: 1px; background-color: #f2f2f2; margin: 0 auto"></view>
			</block>
		</view>
		<!-- 评论回复 -->
		<view v-if="TabCur == 2" style="background-color: #ffffff">
			<block v-for="(item, dataListindex) in ping_hui_list" :key="dataListindex">
				<view style="background-color: #fff; overflow: hidden" class="weui-cells weui-cells_after-title">
					<view style="padding-bottom: 10px">
						<!-- 头像 -->
						<view class="cu-list menu-avatar">
							<view class="cu-item">
								<view class="cu-avatar round lg"
									:style="'background-image:url(' + item.user_head_sculpture + ');'"></view>
								<view class="content flex-sub">
									<view class="align-center">
										<view>{{ item.user_nick_name }}</view>
									</view>
									<view class="text-gray text-sm flex justify-between">
										<text style="font-size: 13px; color: #888888">{{ item.duplex_time }}</text>
									</view>
								</view>
							</view>
						</view>
						<!-- 头像 -->
						<!-- 内容 -->
						<view>
							<view class="weui-cell" style="padding: 0rpx 10px 10px 20px">
								<view class="weui-cell__hd"
									style="font-size: 16px; position: relative; margin-right: 10px">
									<rich-text :nodes="item.duplex_content"></rich-text>
								</view>
							</view>
						</view>
						<!-- 内容 -->
						<view style="clear: both; height: 0"></view>
						<view class="" style="height: 40px; padding-top: 10px">
							<view style="float: right; margin-right: 15px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 1)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #33cc33; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核通过
								</button>
							</view>
							<view style="float: right; margin-right: 25px" class="weui-flex__item">
								<button @tap="get_this_id(item.id, dataListindex, 2)" hover-class="none"
									style="padding: 0px; color: #fff; background-color: #ff3333; font-size: 13px; width: 70px; height: 30px; line-height: 30px">
									审核拒绝
								</button>
							</view>
						</view>
					</view>
				</view>

				<view style="width: 93%; height: 1px; background-color: #f2f2f2; margin: 0 auto"></view>
			</block>
		</view>

		<view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>

		<view :class="'cu-modal ' + (ok ? 'show' : '')">
			<view class="cu-dialog">
				<view class="cu-bar bg-white justify-end">
					<view class="content">提示</view>
					<view class="action" @tap="hideModal">
						<text class="cuIcon-close text-red"></text>
					</view>
				</view>
				<view class="padding-xs">
					<view class="bg-white" v-if="key == 1">确定通过审核?</view>
					<textarea v-if="key == 2 && ok" @input="is_qq_text"
						style="height: 5em; width: 100%; padding: 10px; font-size: 13px; background-color: #fff"
						placeholder="请填写拒绝理由" />
				</view>
				<view class="cu-bar bg-white justify-end">
					<view class="action">
						<button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
						<button class="cu-btn bg-green margin-left" @tap="submit_status">确定</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import http from '../../../util/http.js';
	const innerAudioContext = uni.getBackgroundAudioManager();
	const app = getApp();
	export default {
		data() {
			return {
				http_root: app.globalData.http_root,
				new_list: [],
				hui_list: [],
				ping_hui_list: [],
				page: 1,
				hui_page: 1,
				ping_hui_page: 1,
				id: 0,
				ok: false,
				key: 1,
				reject_reason: '',
				data_index: 0,
				TabCur: 0,
				images: [],
				title: '',
				di_msg: false,
				current_audio_index: -1
			};
		},
		onLoad(options) {
			this.TabCur = options.id;
			if (options.id == 0) {
				this.title = '帖子审核';
				this.get_index_list();
			} else if (options.id == 1) {
				this.title = '回复审核';
				this.get_hui_list();
			} else if (options.id == 2) {
				this.title = '评论回复';
				this.get_ping_hui_list();
			}
		},
		onPullDownRefresh() {
			if (this.TabCur == 0) {
				this.new_list = [];
				this.page = 1;
				this.get_index_list();
			} else if (this.TabCur == 1) {
				this.hui_list = [];
				this.hui_page = 1;
				this.get_hui_list();
			} else {
				this.ping_hui_list = [];
				this.ping_hui_page = 1;
				this.get_ping_hui_list();
			}
			setTimeout(()=> {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		onReachBottom() {
			if (this.TabCur == 0) {
				this.page++;
				this.get_index_list();
			} else if (this.TabCur == 1) {
				this.hui_page++;
				this.get_hui_list();
			} else {
				this.ping_hui_page++;
				this.get_ping_hui_list();
			}
		},
		methods: {
			home_url(key, id, type) {
				let url = '';
				if (key == 1) {
					url = '/yl_welore/pages/packageB/my_home/index?id=' + id;
				} else if (key == 2) {
					url = '/yl_welore/pages/packageA/circle_info/index?id=' + id;
				} else if (key == 3) {
					url = '/yl_welore/pages/packageA/article/index?id=' + id + '&type=' + type;
				}
				if (url) {
					uni.navigateTo({
						url: url
					});
				}
			},
			togglePlay(item, index) {
				if (item.is_voice) {
					this.stop(index);
				} else {
					let src = item.study_voice || item.reply_voice;
					this.play(src, index);
				}
			},
			play(src, index) {
				if (this.current_audio_index !== -1 && this.current_audio_index !== index) {
					this.stop(this.current_audio_index);
				}
				this.current_audio_index = index;

				innerAudioContext.src = src;
				innerAudioContext.title = '暂无标题';
				innerAudioContext.play();

				const targetList = this.TabCur == 0 ? this.new_list : this.hui_list;
				if (targetList[index]) {
					this.$set(targetList[index], 'is_voice', true);
				}

				innerAudioContext.onTimeUpdate(() => {
					const targetList = this.TabCur == 0 ? this.new_list : this.hui_list;
					if (targetList[index] && targetList[index].is_voice) {
						const currentTime = parseInt(innerAudioContext.currentTime);
						const min = '0' + parseInt(currentTime / 60);
						let sec = currentTime % 60;
						if (sec < 10) {
							sec = '0' + sec;
						}
						const starttime = min + ':' + sec;
						this.$set(targetList[index], 'starttime', starttime);
						this.$set(targetList[index], 'offset', innerAudioContext.currentTime);
					}
				});
				innerAudioContext.onEnded(() => {
					const targetList = this.TabCur == 0 ? this.new_list : this.hui_list;
					if (targetList[index]) {
						this.$set(targetList[index], 'is_voice', false);
						this.$set(targetList[index], 'starttime', '00:00');
						this.$set(targetList[index], 'offset', 0);
					}
					this.current_audio_index = -1;
				});
			},
			stop(index) {
				innerAudioContext.pause();
				const targetList = this.TabCur == 0 ? this.new_list : this.hui_list;
				if (targetList[index]) {
					this.$set(targetList[index], 'is_voice', false);
				}
				if (this.current_audio_index === index) {
					this.current_audio_index = -1;
				}
			},
			sliderChange(e, index) {
				const offset = parseInt(e.detail.value);
				const targetList = this.TabCur == 0 ? this.new_list : this.hui_list;
				if (targetList[index]) {
					let src = targetList[index].study_voice || targetList[index].reply_voice;
					this.play(src, index);
					innerAudioContext.seek(offset);
				}
			},
			get_this_id(id, index, key) {
				this.id = id;
				this.key = key;
				this.ok = true;
				this.data_index = index;
			},
			hideModal() {
				this.ok = false;
				this.reject_reason = '';
			},
			is_qq_text(e) {
				this.reject_reason = e.detail.value;
			},
			submit_status() {
				var e = app.globalData.getCache("userinfo");
				let url = '';
				let params = {
					token: e.token,
					openid: e.openid,
					key: this.key,
					reject_reason: this.reject_reason
				};

				if (this.TabCur == 0) {
					url = app.globalData.api_root + 'User/set_paper_status';
					params.paper_id = this.id;
				} else if (this.TabCur == 1) {
					url = app.globalData.api_root + 'Index/set_reply_status';
					params.reply_id = this.id;
				} else if (this.TabCur == 2) {
					url = app.globalData.api_root + 'Index/set_ping_hui_status';
					params.ping_id = this.id;
				}

				http.POST(url, {
					params,
					success: (res) => {
						uni.showToast({
							title: res.data.msg,
							icon: 'none',
							duration: 2000
						});
						if (res.data.status == 'success') {
							this.hideModal();
							this.delItemFn(this.data_index);
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: () => {}
						});
					}
				});
			},
			delItemFn(index) {
				if (this.TabCur == 0) {
					this.new_list.splice(index, 1);
				} else if (this.TabCur == 1) {
					this.hui_list.splice(index, 1);
				} else {
					this.ping_hui_list.splice(index, 1);
				}
			},
			get_ping_hui_list() {
				var e = app.globalData.getCache("userinfo");
				const params = {
					token: e.token,
					openid: e.openid,
					ping_hui_page: this.ping_hui_page
				};
				http.POST(app.globalData.api_root + 'Index/get_ping_hui_list_admin', {
					params,
					success: (res) => {
						if (res.data.status == 'success') {
							this.ping_hui_list = this.ping_hui_list.concat(res.data.info);
							if (res.data.info.length < 5) {
								this.di_msg = true;
							}
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: () => {}
						});
					}
				});
			},
			get_hui_list() {
				var e = app.globalData.getCache("userinfo");
				const params = {
					token: e.token,
					openid: e.openid,
					hui_page: this.hui_page
				};
				http.POST(app.globalData.api_root + 'Index/get_hui_list_admin', {
					params,
					success: (res) => {
						console.log(res);
						if (res.data.status == 'success') {
							this.hui_list = this.hui_list.concat(res.data.info);
							if (res.data.info.length < 5) {
								this.di_msg = true;
							}
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: () => {}
						});
					}
				});
			},
			get_index_list() {
				var e = app.globalData.getCache("userinfo");
				const params = {
					token: e.token,
					openid: e.openid,
					index_page: this.page
				};
				http.POST(app.globalData.api_root + 'User/get_index_list_admin', {
					params,
					success: (res) => {
						console.log(res);
						if (res.data.status == 'success') {
							this.new_list = this.new_list.concat(res.data.info);
							if (res.data.info.length < 5) {
								this.di_msg = true;
							}
						}
					},
					fail: () => {
						uni.showModal({
							title: '提示',
							content: '网络繁忙，请稍候重试！',
							showCancel: false,
							success: () => {}
						});
					}
				});
			}
		}
	};
</script>
<style>
	page {
		background-color: #fff;
	}

	.fixed {
		position: relative !important;
	}

	/**index.wxss**/
	.audiosBox {
		width: 92%;
		margin: auto;
		height: 130rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #f6f7f7;
		border-radius: 10rpx;
	}

	/*按钮大小  */
	.audioOpen {
		width: 70rpx;
		height: 70rpx;
		border: 2px solid #4c9dee;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}

	.image2 {
		margin-left: 10%;
	}

	/*进度条长度  */
	.slid {
		flex: 1;
		position: relative;
	}

	.slid view {
		display: flex;
		justify-content: space-between;
	}

	.slid view>text:nth-child(1) {
		color: #4c9dee;
		margin-left: 6rpx;
	}

	.slid view>text:nth-child(2) {
		margin-right: 6rpx;
	}

	slider {
		width: 520rpx;
		margin: 0;
		margin-left: 35rpx;
	}

	/*横向布局  */
	.times {
		width: 100rpx;
		text-align: center;
		display: inline-block;
		font-size: 24rpx;
		color: #999999;
		margin-top: 5rpx;
	}

	.title view {
		text-indent: 2em;
	}
</style>