<template>
  <view>
    <cu-custom bgColor="none" :isSearch="false" :isBack="true">
      <view slot="backText">返回</view>
      <view slot="content" style="color: #2c2b2b;font-weight: 600; font-size: 36rpx;">任务中心</view>
    </cu-custom>
    <view class="main-container">
      <view class="content-card">
        <view class="tab-container">
          <view class="flex justify-center">
            <view @tap="handleChange('tab1')" :class="'tab-item ' + (current == 'tab1' ? 'active' : '')">
              <text :class="'tab-text ' + (current == 'tab1' ? 'active' : 'inactive')">任务列表</text>
            </view>
            <view @tap="handleChange('tab2')" :class="'tab-item ' + (current == 'tab2' ? 'active' : '')"
              style="margin-left: 20rpx;">
              <text :class="'tab-text ' + (current == 'tab2' ? 'active' : 'inactive')">完成记录</text>
            </view>
          </view>
        </view>
        <view class="task-section" v-if="current == 'tab1'">
          <view v-for="(item, list_index) in list">
            <view v-for="(i_data, item_index) in item.list">
              <!-- 任务分类标题 -->
              <view class="task-category-header" v-if="item_index == 0">
                <view class="task-category-title">
                  <text class="task-category-icon cuIcon-titles"></text>
                  <text>{{ item.title }}</text>
                </view>
              </view>
              <!-- 任务卡片 -->
              <view class="task-item-card">
                <view class="flex justify-between align-center">
                  <view class="flex-1">
                    <!-- 任务名称和进度 -->
                    <view class="task-name">{{ i_data.task_name }}</view>
                    <view class="task-progress">进度：{{ i_data.count }}/{{ i_data.task_frequency }}</view>

                    <!-- 奖励信息 -->
                    <view class="reward-container">
                      <view v-if="vip == 1" class="reward-capsule">
                        <view class="reward-type-tag vip">
                          <text v-if="i_data.task_reward_type == 0">{{ $state.diy.confer }}</text>
                          <text v-if="i_data.task_reward_type == 1">经验值</text>
                          <text v-if="i_data.task_reward_type == 2">荣誉点</text>
                        </view>
                        <view class="reward-amount-tag vip">
                          VIP专属+{{ i_data.rich_task_salary }}
                        </view>
                      </view>
                      <view v-if="vip == 0" class="reward-capsule">
                        <view class="reward-type-tag normal">
                          <text v-if="i_data.task_reward_type == 0">{{ $state.diy.confer }}</text>
                          <text v-if="i_data.task_reward_type == 1">经验值</text>
                          <text v-if="i_data.task_reward_type == 2">荣誉点</text>
                        </view>
                        <view class="reward-amount-tag normal">
                          +{{ i_data.poor_task_salary }}
                        </view>
                      </view>
                    </view>
                  </view>

                  <!-- 操作按钮 -->
                  <view>
                    <button class="action-button btn-complete" @tap="get_url(i_data.task_type)"
                      v-if="i_data.task_frequency > i_data.count && i_data.ok_count == 0">去完成</button>
                    <button class="action-button btn-completed" v-if="i_data.ok_count > 0">已完成</button>
                    <button :disabled="bind_ok" class="action-button btn-receive" @tap="receive(i_data.id)"
                      v-if="i_data.count >= i_data.task_frequency && i_data.ok_count == 0">领取</button>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="task-section" v-if="current == 'tab2'">
          <view v-for="(item, index) in my_list">
            <!-- 完成记录卡片 -->
            <view class="record-item">
              <view class="record-description">{{ item.complete_description }}</view>
              <view class="record-time">{{ item.complete_time }}</view>
              <view class="record-reward">
                +{{ item.task_salary }}
                <text v-if="item.task_reward_type == 0">{{ $state.diy.confer }}</text>
                <text v-if="item.task_reward_type == 1">经验值</text>
                <text v-if="item.task_reward_type == 2">荣誉点</text>
              </view>
            </view>

            <!-- 分隔线 -->
            <view class="record-divider" v-if="index < my_list.length - 1"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const app = getApp();
let rewardedVideoAd = null;
import http from "../../../util/http.js";
export default {
  data() {
    return {
      current: 'tab1',
      height: app.globalData.height,
      list: [],
      my_list: [],
      vip: 0,
      page: 1,
      copyright: {},
      bind_ok: false
    }
  },
  onLoad(options) {
    let that = this;
    if (uni.createRewardedVideoAd) {
      rewardedVideoAd = uni.createRewardedVideoAd({
        adUnitId: getApp().globalData.store.getState().copyright['jili']
      });
      rewardedVideoAd.onLoad(() => {
        console.log('onLoad event emit');
      });
      rewardedVideoAd.onError(err => {
        console.log('onError event emit', err);
        uni.showModal({
          title: '提示',
          content: '准备广告中，请稍后重试',
          success(res) {
            if (res.confirm) {
              console.log('用户点击确定');
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      });
      rewardedVideoAd.onClose(res => {
        if (res && res.isEnded) {
          that.set_ad();
        } else { }
      });
    }
  },
  onShow() {
    this.get_task();
    this.get_task_logger();
    let copyright = getApp().globalData.store.getState().copyright;
    this.copyright = copyright;
    console.log(this.copyright);
  },
  methods: {
    set_ad() {
      const b = app.globalData.api_root + 'Task/set_ad';
      let that = this;
      const e = app.globalData.getCache("userinfo");
      let params = new Object();
      params.token = e.token;
      params.type = 0;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    handleChange(key) {
      this.current = key;
    },
    get_task() {
      const b = app.globalData.api_root + 'Task/get_task';
      let that = this;
      const e = app.globalData.getCache("userinfo");
      let params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          that.list = res.data.info;
          that.vip = res.data.vip;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    get_task_logger() {
      const b = app.globalData.api_root + 'Task/get_task_logger';
      let that = this;
      const e = app.globalData.getCache("userinfo");
      let params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.page = this.page;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          that.my_list = res.data.info;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    receive(task_id) {
      this.bind_ok = true;
      const b = app.globalData.api_root + 'Task/task_receive';
      let that = this;
      const e = app.globalData.getCache("userinfo");
      let params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.task_id = task_id;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.get_task();
            that.get_task_logger();
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
          that.bind_ok = false;
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    get_url(type) {
      if (type == 0 || type == 1 || type == 2 || type == 3 || type == 4 || type == 5 || type == 7) {
        uni.switchTab({
          url: '/yl_welore/pages/index/index'
        });
        return;
      }
      if (type == 6 && this.copyright.recharge_arbor == 1) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/user_details/index'
        });
        return;
      }
      if (type == 8) {
        uni.switchTab({
          url: '/yl_welore/pages/shell_mall/index'
        });
        return;
      }
      if (type == 9) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/user_invitation/index'
        });
        return;
      }
      if (type == 10) {
        uni.switchTab({
          url: '/yl_welore/pages/user/index'
        });
        return;
      }
      if (type == 11 && this.copyright.ios_pay_arbor == 1 && this.copyright.noble_arbor == 1) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/user_vip/index'
        });
        return;
      }
      if (type == 12) {
        rewardedVideoAd.show().catch(() => {
          rewardedVideoAd.load().then(() => rewardedVideoAd.show()).catch(err => {
            uni.showModal({
              title: '提示',
              content: '准备广告中，请稍后重试',
              success(res) {
                if (res.confirm) {
                  console.log('用户点击确定');
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          });
        });
      }
    }
  }
};
</script>
<style>
page {
  background: linear-gradient(135deg, #fff1eb 0%, #ace0f9 50%, #e8f4fd 100%);
}

/* 主容器样式 */
.main-container {
  padding: 20rpx;
  background: transparent;
}

/* 内容卡片容器 */
.content-card {
  background: #ffffff;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(74, 158, 255, 0.12);
  margin-top: 20rpx;
}

/* 标签页容器优化 */
.tab-container {
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  padding: 30rpx 20rpx 20rpx;
  position: relative;
}

.tab-item {
  position: relative;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #4a9eff 0%, #2980b9 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(74, 158, 255, 0.4);
}

.tab-text {
  font-weight: 600;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.tab-text.active {
  color: #ffffff;
}

.tab-text.inactive {
  color: #687A89;
}

/* 任务卡片样式优化 */
.task-section {
  background: #ffffff;
  border-radius: 0 0 32rpx 32rpx;
}

.task-category-header {
  background: linear-gradient(135deg, #f0f6fd 0%, #fff8f0 100%);
  padding: 30rpx 20rpx;
  border-left: 6rpx solid #ffb366;
  margin: 20rpx 0;
}

.task-category-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333333;
  display: flex;
  align-items: center;
}

.task-category-icon {
  margin-right: 16rpx;
  color: #ffb366;
}

.task-item-card {
  background: #ffffff;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f2f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-item-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(74, 158, 255, 0.03) 0%, rgba(255, 179, 102, 0.02) 100%);
  pointer-events: none;
}

.task-item-card:active {
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.12);
}

.task-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.task-progress {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

/* 奖励标签优化 */
.reward-container {
  margin: 20rpx 0;
}

.reward-capsule {
  display: inline-flex;
  border-radius: 50rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.reward-type-tag {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  color: #ffffff;
  border-radius: 30rpx 0px 0px 30rpx;
}

.reward-type-tag.vip {
  background: linear-gradient(135deg, #ffb366 0%, #ff9500 100%);
}

.reward-type-tag.normal {
  background: linear-gradient(135deg, #4a9eff 0%, #2980b9 100%);
}

.reward-amount-tag {
  padding: 12rpx 20rpx;
  font-size: 22rpx;
  font-weight: 600;
  background: #ffffff;
  border: 2rpx solid;
  border-radius: 0px 30rpx 30rpx 0px;
}

.reward-amount-tag.vip {
  border-color: #ffb366;
  color: #ff9500;
}

.reward-amount-tag.normal {
  border-color: #4a9eff;
  color: #2980b9;
}

/* 按钮样式优化 */
.action-button {
  border-radius: 50rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.btn-complete {
  background: linear-gradient(135deg, #4a9eff 0%, #2980b9 100%);
  color: #ffffff;
}

.btn-complete:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(74, 158, 255, 0.5);
}

.btn-completed {
  background: linear-gradient(135deg, #f0f6fd 0%, #e1f0fc 100%);
  color: #7ba7d9;
  border: 1rpx solid #c8e1f7;
}

.btn-receive {
  background: linear-gradient(135deg, #20b2aa 0%, #7fdbda 100%);
  color: #ffffff;
}

.btn-receive:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(32, 178, 170, 0.5);
}

/* 完成记录样式优化 */
.record-item {
  background: #ffffff;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(74, 158, 255, 0.08);
  position: relative;
  border-left: 6rpx solid #2980b9 ;
}

.record-description {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999999;
}

.record-reward {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 30rpx;
  font-weight: 600;
  color: #4a9eff;
  background: linear-gradient(135deg, #4a9eff , #7fdbda);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.record-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent 0%, #e8e8e8 50%, transparent 100%);
  margin: 0 20rpx;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.task-item-card {
  animation: fadeInUp 0.6s ease-out;
}

.record-item {
  animation: slideIn 0.5s ease-out;
}

.tab-item {
  animation: slideIn 0.4s ease-out;
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .task-name {
    font-size: 28rpx;
  }

  .task-progress {
    font-size: 22rpx;
  }

  .action-button {
    padding: 16rpx 32rpx;
    font-size: 24rpx;
  }
}
</style>