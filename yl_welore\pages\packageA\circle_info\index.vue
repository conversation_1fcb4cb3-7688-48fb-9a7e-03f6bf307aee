<template>
  <view>
    <view class="nav-wrap" style="height: 440rpx;position:relative;">
      <cu-custom :bgColor="top_back" :isSearch="false" :isBack="true" :ShowUid="false" style="color: #fff;">
        <view slot="backText">返回</view>
        <view slot="content"
          :style="'color: ' + (!top_back ? '#FFFFFF' : '#000000') + ';font-weight: 600; font-size: 36rpx;'">
          {{ getInfo.realm_name }}</view>
      </cu-custom>
      <view @tap="url_is_b" style="z-index: 1;padding:20px 15px 10px 15px;height:240rpx;">
        <view class="cu-avatar round lg"
          :style="'float:left;background-color:#fff;background-image:url(' + (getInfo.realm_icon) + ');'">
          <view v-if="getInfo.attention == 1" class="cu-tag badge cuIcon-lock bg-red"></view>
          <view v-if="getInfo.attention == 2" class="cu-tag badge cuIcon-vip bg-yellow"></view>
        </view>
        <view style="float:left;margin-left: 20rpx;margin-top: 6rpx;">
          <view style="font-size:18px;font-weight:600;color:#FFF;">{{ getInfo.realm_name }}</view>
          <view style="font-size: 12px;color: #FFF;">已加入 {{ getInfo.concern }}　|　帖子 {{ getInfo.is_paper_count }}</view>
        </view>
        <view style="float: right;color: #FFFFFF;margin-top: 20rpx;">
          <view v-if="getInfo.attention != 2">
            <button
              @tap.stop.prevent="parseEventDynamicCode($event, getInfo.attention == 1 ? 'set_trailing' : 'add_trailing')"
              v-if="getInfo.is_trailing == false" class="cu-btn bg-white shadow round" role="button"
              :aria-disabled="false">加入</button>
          </view>
          <view v-if="getInfo.attention == 2">
            <button @tap.stop.prevent="add_trailing" v-if="getInfo.is_trailing == false"
              class="cu-btn bg-white shadow round" role="button" :aria-disabled="false">加入</button>
          </view>
          <button @tap.stop.prevent="add_trailing" v-if="getInfo.is_trailing == true"
            class="cu-btn bg-gray shadow round" role="button" :aria-disabled="false">退出</button>
        </view>
        <view style="clear:both;height:0"></view>
        <view class="flex justify-between align-center" style="margin-top: 30rpx;">
          <view class="text_num" style="font-size: 12px;color: #FFF;width: 75%;">{{ getInfo.realm_synopsis }}</view>
          <view class="cu-avatar-group">
            <view class="cu-avatar round sm" :style="'background-image:url(' + (item.user_head_sculpture) + ');'"
              v-for="(item, index) in (getInfo.da_qq)" :key="index"></view>
          </view>
          <view><text class="cicon-angle text-white"></text></view>
        </view>
        <view v-if="getInfo"
          style="height: 440rpx;position: absolute;width: 100%;top: 0;left:0%;z-index: -1;overflow: hidden;">
          <image v-if="getInfo.realm_back_img != null" mode="aspectFill" style="width: 100%;height: 100%;"
            :src="getInfo.realm_back_img"></image>
          <view v-else style="height: 440rpx;background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);">
            <view class="stars"></view>
            <view class="stars2"></view>
            <view class="stars3"></view>
          </view>
          <!-- <image wx:if="{{getInfo.realm_back_img==null}}" mode="aspectFill" style="width: 100%;height: 195px;-webkit-filter: blur(15px);filter: blur(15px);transform: scale(1.2);" src="{{getInfo.realm_icon}}"></image> -->

        </view>
      </view>
    </view>

    <!-- 置顶 -->
    <!-- 置顶内容区域 -->
    <view v-if="getInfo.msg_key == 0 && top_list.length != 0">
      <view class="top-content-container">
        <view class="top-item-wrapper" @tap="open_url" :data-id="item.id" :data-key="item.study_type"
          v-for="(item, index) in (top_list)" :key="index">
          <view class="top-badge">
            <text class="top-label">置顶</text>
          </view>
          <view class="top-content-text">
            {{ item.study_title == '' ? item.study_content : item.study_title }}
          </view>
          <view class="top-arrow">
            <text class="cuIcon-right text-gray"></text>
          </view>
        </view>
        <view class="top-divider-line"></view>
      </view>
    </view>

    <view v-if="getInfo.msg_key == 0 && version == 0" style="width:100%;height:35px;">
      <view style="float:left;padding:10px;" @tap="open_qr_code">
        <text class="cuIcon-qr_code  text-black" style="font-size: 17px;vertical-align: middle;"></text>
        <text style="vertical-align: middle;margin-left: 5px;">进入更多交流</text>
      </view>
      <view @tap="handleCancel" style="color:#999;float:right;padding:10px;">
        内容排序：
        {{ actions_name }}
        <text class="cuIcon-right lg text-gray"></text>
      </view>
    </view>
    <view style="clear:both;height:0"></view>
    <ad-custom v-if="$state.ad.info.lattice_twig == 1" :unit-id="$state.ad.info.lattice_id" ad-type="grid"
      grid-opacity="0.8" grid-count="5" ad-theme="white"></ad-custom>
    <!-- 标签页导航 -->
    <view class="tab-navigation-container" v-if="getInfo.msg_key == 0">
      <view class="tab-scroll-wrapper">
        <block v-if="copyright.tory_sort_arbor == 0">
          <view @tap="handleChange_tg" data-key="tab1"
            :class="['tab-item-modern', current_tj == 'tab1' ? 'tab-active-modern' : '']">
            <text class="tab-text-modern">最新</text>
          </view>
          <view @tap="handleChange_tg" data-key="tab2"
            :class="['tab-item-modern', current_tj == 'tab2' ? 'tab-active-modern' : '']">
            <text class="tab-text-modern">推荐</text>
          </view>
        </block>
        <block v-if="copyright.tory_sort_arbor == 1">
          <view @tap="handleChange_tg" data-key="tab2"
            :class="['tab-item-modern', current_tj == 'tab2' ? 'tab-active-modern' : '']">
            <text class="tab-text-modern">推荐</text>
          </view>
          <view @tap="handleChange_tg" data-key="tab1"
            :class="['tab-item-modern', current_tj == 'tab1' ? 'tab-active-modern' : '']">
            <text class="tab-text-modern">最新</text>
          </view>
        </block>
        <view v-if="version == 0" @tap="handleChange_tg" data-key="tab3"
          :class="['tab-item-modern', current_tj == 'tab3' ? 'tab-active-modern' : '']">
          <text class="tab-text-modern">视频</text>
        </view>
        <view @tap="handleChange_tg" data-key="tab4"
          :class="['tab-item-modern', current_tj == 'tab4' ? 'tab-active-modern' : '']">
          <text class="tab-text-modern">活动</text>
        </view>
        <view v-if="version == 0" @tap="handleChange_tg" data-key="tab5"
          :class="['tab-item-modern', current_tj == 'tab5' ? 'tab-active-modern' : '']">
          <text class="tab-text-modern">有声</text>
        </view>
      </view>
    </view>
 
    <view style="clear:both;height:0"></view>
    <Index1 ref="indexComp" v-if="mod.home == '0'" :parentData="currentInstance" @home-url="home_url"
      @gambit-list="gambit_list" @dian-option="dian_option" @vote-do="vote_do" @play="play" @stop="stop"
      @slider-change="sliderChange" @home-pl="home_pl" @dynamic-code="handleDynamicCode"></Index1>
    <Index2 ref="indexComp" v-if="mod.home == 'aa3b1c88-2d41-9cde-cff7-55372169e4eb'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index2>
    <Index3 ref="indexComp" v-if="mod.home == 'be454a15-e373-f773-376b-127f3a35d3c6'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index3>
    <Index4 ref="indexComp" v-if="mod.home == '453776a4-6724-fd4f-4ff1-48363b245915'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index4>
    <Index5 ref="indexComp" v-if="mod.home == 'd5b2d78e-3152-ee54-aca8-a4402adc601b'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index5>
    <Index6 ref="indexComp" v-if="mod.home == '58567383-612e-ca8f-116d-89e1057eb02a'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index6>
    <Index7 ref="indexComp" v-if="mod.home == '9701db92-a7e1-bdd7-842e-9e9bea168127'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index7>
    <Index8 ref="indexComp" v-if="mod.home == '47a436a1-6541-a7a0-5723-61d7fe40b7c3'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index8>
    <Index9 ref="indexComp" v-if="mod.home == '57228047-ad66-b5c0-2970-0be62de79377'"
      :parentData="currentInstance" @home-url="home_url" @gambit-list="gambit_list" @dian-option="dian_option"
      @vote-do="vote_do" @play="play" @stop="stop" @slider-change="sliderChange" @home-pl="home_pl"
      @dynamic-code="handleDynamicCode"></Index9>

    <view v-if="getInfo.msg_key == 1" class="padding-xl radius shadow-warp bg-white margin">
      <view style="text-align: center;font-size:13px;margin-top:5px;">
        <text class="cuIcon-lock"></text>　您还没有加入该{{ $state.diy.landgrave }}无法查看相关内容
      </view>
    </view>
    <view v-if="getInfo.msg_key == 2" class="padding-xl radius shadow-warp bg-white margin">
      <view style="text-align: center;font-size:13px;margin-top:5px;">
        <text class="cuIcon-lock"></text>
        　该{{ $state.diy.landgrave }}只允许会员访问
      </view>
    </view>

    <view v-if="getInfo.msg_key == 3" class="padding-xl radius shadow-warp bg-white margin">
      <view style="text-align: center;font-size:13px;margin-top:5px;">
        <text class="cuIcon-lock"></text>
        　该{{ $state.diy.landgrave }}阅读权限「Lv.{{ getInfo.visit_level }}{{ getInfo.level.level_name }}」
      </view>
    </view>

    <view v-if="new_list.length == 0 && getInfo.msg_key == 0" class="padding-xl radius shadow-warp bg-white margin">
      <view style="text-align: center;font-size:13px;margin-top:5px;">
        <text class="cuIcon-search"></text>
        {{ $state.diy.landgrave }}还没有发布任何内容
      </view>
    </view>

    <view class="modal-mask" @touchmove.stop.prevent="preventTouchMove" v-if="home_pl_check"></view>
    <view class="modal-dialog" v-if="home_pl_check">
      <view class="modal-title" style="font-size:15px;">评论</view>
      <view class="modal-content">
        <textarea @input="home_pl_cai" style="height:5em;width:90%;padding:10px;font-size:13px;"
          placeholder="欢迎吐槽..." />
      </view>
      <view class="modal-footer">
        <view class="btn-cancel" style="font-size:15px;" @tap="hideModal" data-status="cancel">取消</view>
        <view class="btn-confirm" style="font-size:15px;" @tap="do_user_pl" data-status="confirm">确定</view>
      </view>
    </view>

    <view
      v-if="(getInfo.attention == 1 && getInfo.is_trailing == true) || (getInfo.attention == 2 && getInfo.is_trailing == true)"
      class="weui-tabbar_boo" :animation="animBack" style="height:55px;">

      <view @tap="nav_add" data-k="toupiao" class="img-style" :animation="animCollect">
        <image v-if="$state.diy.user_vip.vote_member == 1 && version == 0"
          style="width:35px;height:35px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['graffito']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">{{ add['graffito']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="yuyin" class="img-style" :animation="animTranspond">
        <image v-if="$state.diy.user_vip.voice_member == 1 && version == 0"
          style="width:35px;height:35px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['audio']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['audio']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="tuwen" class="img-style" :animation="animInput">
        <image style="width:100%;height:100%;" :src="add['writing']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">{{ add['writing']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="shipin" v-if="version == 0" class="img-style" :animation="animationM">
        <image v-if="$state.diy.user_vip.video_member == 1 && version == 0"
          style="width:35px;height:35px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['video']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['video']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="huodong" class="img-style" :animation="animationHD">
        <image v-if="$state.diy.user_vip.brisk_member == 1"
          style="width:35px;height:35px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['brisk']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['brisk']['title'] }}</view>
      </view>
      <image :src="$state.diy.pattern_data['release']['images']['img']" class="img-plus-style" :animation="animPlus"
        @tap="plus"></image>
    </view>

    <view v-if="getInfo.attention == 0" class="weui-tabbar_boo" :animation="animBack" style="height:55px;">

      <view @tap="nav_add" data-k="toupiao" class="img-style" :animation="animCollect">
        <image v-if="$state.diy.user_vip.vote_member == 1 && version == 0"
          style="width:15px;height:15px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['graffito']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">{{ add['graffito']['title'] }}</view>
      </view>

      <view @tap="nav_add" data-k="yuyin" class="img-style" :animation="animTranspond">
        <image v-if="$state.diy.user_vip.voice_member == 1 && version == 0"
          style="width:15px;height:15px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['audio']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['audio']['title'] }}</view>
      </view>
      <view @tap="nav_add" data-k="tuwen" class="img-style" :animation="animInput">
        <image style="width:100%;height:100%;" :src="add['writing']['images']"></image>
        <view style="text-align:center;margin-top:-5px;font-size:12px;">{{ add['writing']['title'] }}</view>
      </view>
      <view @tap="nav_add" data-k="shipin" v-if="version == 0" class="img-style" :animation="animationM">
        <image v-if="$state.diy.user_vip.video_member == 1 && version == 0"
          style="width:15px;height:15px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['video']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['video']['title'] }}</view>
      </view>
      <view @tap="nav_add" data-k="huodong" class="img-style" :animation="animationHD">
        <image v-if="$state.diy.user_vip.brisk_member == 1"
          style="width:15px;height:15px;position:absolute;right:0;bottom:0;z-index:100;"
          :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"></image>
        <image style="width:100%;height:100%;" :src="add['brisk']['images']"></image>
        <view style="text-align:center;font-size:12px;margin-top:-5px;">{{ add['brisk']['title'] }}</view>
      </view>
      <image :src="$state.diy.pattern_data.release.images.img" class="img-plus-style" :animation="animPlus" @tap="plus">
      </image>
    </view>


    <view :class="'cu-modal ' + (guanzhu ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">提示</view>
          <view class="action" @tap="hideModal">
            <text class="cuIcon-close text-red"></text>
          </view>
        </view>
        <view class="">
          <scroll-view scroll-x class="bg-white nav text-center">
            <view :class="'cu-item ' + (set_guanzhu == 'tab1' ? 'text-blue cur' : '')" @tap="handleChange_gz"
              data-key="tab1" style="margin: 0 60rpx;">
              <text :class="set_guanzhu == 'tab1' ? '_this' : ''" style="font-weight:700;">申请加入</text>
            </view>
            <view v-if="getInfo.atence == 1" :class="'cu-item ' + (set_guanzhu == 'tab2' ? 'text-blue cur' : '')"
              @tap="handleChange_gz" data-key="tab2" style="margin: 0 60rpx;">
              <text :class="set_guanzhu == 'tab2' ? '_this' : ''" style="font-weight:700;">我有暗号</text>
            </view>
          </scroll-view>
          <view v-if="guanzhu">
            <textarea class="bg-white" v-if="set_guanzhu == 'tab1'" @input="get_gz_text_do" maxlength="140"
              placeholder="申请原因，便于管理员审核"
              style="width:100%;height:150px;padding-top:10px;font-size:14px;text-align: left;padding: 15px;"></textarea>
            <input class="bg-white" v-if="set_guanzhu == 'tab2'" @input="get_gz_input_do" placeholder="输入暗号直接加入"
              type="text" style="padding: 10px;font-size: 14px;margin: 10px;height: 40px;" />
          </view>
        </view>
        <view class="cu-bar bg-white justify-end">
          <view class="action">
            <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
            <button class="cu-btn bg-green margin-left" @tap="add_trailing">确定</button>

          </view>
        </view>
      </view>
    </view>


    <!-- 排序选择弹窗 -->
    <view :class="'sort-modal-overlay ' + (visible ? 'show' : '')" @tap="handleCancel1">
      <view class="sort-modal-dialog" catchtap>
        <view class="sort-modal-header">
          <text class="sort-modal-title">🔄 选择排序方式</text>
          <view class="sort-modal-close" @tap="handleCancel1">
            <text class="cuIcon-close"></text>
          </view>
        </view>
        <view class="sort-options-list">
          <view class="sort-option-item" @tap="handleClickItem1" :data-index="a_index"
            v-for="(item, a_index) in actions" :key="a_index"
            :class="item.type == order_time ? 'sort-option-active' : ''">
            <view class="sort-option-content">
              <text class="sort-option-icon">
                {{ item.type == 'fatie' ? '🕒' : item.type == 'huifu' ? '💬' : '👍' }}
              </text>
              <text class="sort-option-text">{{ item.name }}</text>
            </view>
            <view class="sort-option-check" v-if="item.type == order_time">
              <text class="cuIcon-roundcheckfill"></text>
            </view>
          </view>
        </view>
      </view>
    </view>


    <login id="login" @checkPhoen="check_user_login = false;" :check_user_login="check_user_login"></login>
    <phone id="phone" @close_phone_modal="check_phone_show = false;" :check_phone="check_phone_show"></phone>
  </view>
</template>

<script>
import login from "@/yl_welore/util/user_login/login";
import phone from "@/yl_welore/util/user_phone/phone";
import Index1 from "@/yl_welore/pages/index/index1.vue";
import Index2 from "@/yl_welore/pages/index/index2.vue";
import Index3 from "@/yl_welore/pages/index/index3.vue";
import Index4 from "@/yl_welore/pages/index/index4.vue";
import Index5 from "@/yl_welore/pages/index/index5.vue";
import Index6 from "@/yl_welore/pages/index/index6.vue";
import Index7 from "@/yl_welore/pages/index/index7.vue";
import Index8 from "@/yl_welore/pages/index/index8.vue";
import Index9 from "@/yl_welore/pages/index/index9.vue";
var app = getApp();
import http from "../../../util/http.js";
const innerAudioContext = uni.getBackgroundAudioManager();
import regeneratorRuntime from '../../../util/runtime';
let leftHeight = 0,
  rightHeight = 0; //分别定义左右两边的高度
export default {
  components: {
    login,
    phone,
    Index1,
    Index2,
    Index3,
    Index4,
    Index5,
    Index6,
    Index7,
    Index8,
    Index9
  },

  /**
   * 页面的初始数据
   */
  data() {
    return {
      currentInstance: this,
      http_root: app.globalData.http_root,
      uid: 0,
      user_info: {},
      check_phone_show: false,
      visible: false,
      actions_name: '发帖时间',
      actions: [{
        name: '发帖时间',
        type: 'fatie'
      }, {
        name: '最后回帖时间',
        type: 'huifu'
      }, {
        name: '点赞数量',
        type: 'dianzan'
      }],
      show: true,
      title: '',
      //导航栏 中间的标题
      getInfo: {},
      new_list: [],
      leftList: [],
      rightList: [],
      huifu: false,
      animationData: {},
      current: 'tab1',
      current_tj: 'tab1',
      set_guanzhu: 'tab1',
      isPopping: false,
      //是否已经弹出
      animPlus: {},
      //旋转动画
      animCollect: {},
      //item位移,透明度
      animTranspond: {},
      //item位移,透明度
      animInput: {},
      //item位移,透明
      animationHD: {},
      animationM: {},
      animBack: {},
      page: 1,
      page_tj: 1,
      top_list: [],
      //置顶帖子
      di_msg: false,
      guanzhu: false,
      get_gz_text: '',
      get_gz_input: '',
      version: 0,
      //purchase_paper_mod: false, //购买窗口
      //home_pl_check: false, //首页评论框
      //pl_id: 0, //评论ID
      //home_pl_text: '', //首页评论内容
      top_list_style: {
        'height': 60,
        'bottom': 10
      },
      admin: 0,
      mod: {},
      images: [],
      copyright: {},
      check_user_login: false,
      top_back: false,
      scene: 0,
      home: 0,
      dian_index: -1,
      add: {}
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    uni.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    var that = this;
    var scene = decodeURIComponent(options.scene);
    var op = uni.getLaunchOptionsSync();
    if (scene == 'undefined') {
      var id = options.id;
    } else {
      var dd = that.getUrlkey(scene);
      var id = dd.id;
    }
    this.id = id;
    this.scene = op.scene;
    this.new_list = [];
    var order = app.globalData.getCache("order_actions_o");
    if (!order) {
      var order = new Object();
      order.type = 'fatie';
      order.name = '按发帖时间';
      app.globalData.setCache("order_actions_o", order);
    } else {
      this.actions_name = order.name;
    }
    if (op.scene == 1154) {
      this.add_trailing_list();
      this.get_qq_info();
    } else {
      this.doIt();
    }
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var e = app.globalData.getCache("userinfo");
    this.isPopping = false;
    var dd = uni.getStorageSync('is_diy');
    //console.log(dd);
    if (Object.keys(dd).length === 0) {
      this.get_diy();
    } else {
      this.mod = dd.mod;
      this.add = dd.pattern_data.release.list;
      app.globalData.store.setState({
        diy: dd
      });
    }
    //this.takeback();
  },

  onPageScroll(e) {
    var top = e.scrollTop;
    if (top > 200) {
      this.top_back = 'bg-white';
    } else {
      this.top_back = false;
    }
  },
  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    //wx.showNavigationBarLoading() //在标题栏中显示加载
    //模拟加载
    setTimeout(function () {
      uni.hideNavigationBarLoading(); //完成停止加载
      uni.stopPullDownRefresh(); //停止下拉刷新
    }, 1500);
    this.page = 1;
    this.page_tj = 1;
    this.new_list = [];
    this.get_placement_top();
    if (this.current_tj == 'tab1') {
      this.add_trailing_list();
    }
    if (this.current_tj == 'tab2') {
      this.get_tj_list();
    }
    if (this.current_tj == 'tab3') {
      this.get_video_list();
    }
    if (this.current_tj == 'tab4') {
      this.get_activity_list();
    }
    if (this.current_tj == 'tab5') {
      this.get_audio_list();
    }
    this.get_qq_info();
  },
  /**
   * 加载下一页
   */
  onReachBottom() {
    if (this.current_tj == 'tab1') {
      this.page = this.page + 1;
      this.add_trailing_list();
    }
    if (this.current_tj == 'tab2') {
      this.page_tj = this.page_tj + 1;
      this.get_tj_list();
    }
    if (this.current_tj == 'tab3') {
      this.page = this.page + 1;
      this.get_video_list();
    }
    if (this.current_tj == 'tab4') {
      this.page = this.page + 1;
      this.get_activity_list();
    }
    if (this.current_tj == 'tab5') {
      this.page = this.page + 1;
      this.get_audio_list();
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareTimeline() {
    return {
      title: this.getInfo['realm_name'],
      path: '/yl_welore/pages/packageA/circle_info/index?id=' + this.id,
      imageUrl: this.getInfo['realm_icon']
    };
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(d) {
    var forward = app.globalData.forward;
    if (d.from == 'menu') {
      if (forward) {
        return {
          title: forward.title,
          path: '/yl_welore/pages/packageA/circle_info/index?id=' + this.id,
          imageUrl: forward.reis_img
        };
      } else {
        return {
          title: this.getInfo['realm_name'],
          path: '/yl_welore/pages/packageA/circle_info/index?id=' + this.id,
          imageUrl: this.getInfo['realm_icon']
        };
      }
    } else {
      var key = d.target.dataset.key;
      var info = this.new_list[key];
      console.log(info);
      if (forward) {
        return {
          title: forward.title,
          path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.study_type,
          imageUrl: forward.reis_img
        };
      } else {
        let img;
        if (info.image_part) {
          if (info.image_part.length > 0) {
            img = info.image_part[0];
          }
        }
        return {
          title: info.study_title == '' ? info.study_content : info.study_title,
          path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.study_type,
          imageUrl: img
        };
      }
    }
  },
  methods: {
    handleDynamicCode(event, type) {
      if (type === 'check_share') {
        this.check_share();
      } else if (type === 'add_zan') {
        this.add_zan(event);
      }
    },
    async isLeft() {
      const {
        new_list,
        leftList,
        rightList
      } = this;

      // 计算当前总数，用于交替分配的索引
      const currentTotal = leftList.length + rightList.length;

      for (let i = 0; i < new_list.length; i++) {
        const item = new_list[i];
        const globalIndex = currentTotal + i; // 全局索引

        // 为每个项目添加全局索引
        const itemWithIndex = { ...item, globalIndex: globalIndex };

        // 使用交替分配策略，确保左右平衡
        if (globalIndex % 2 === 0) {
          leftList.push(itemWithIndex);
        } else {
          rightList.push(itemWithIndex);
        }

        // 尝试获取高度，但不依赖它
        await this.getBoxHeight(leftList, rightList);
      }
    },
    /**
     * 更新 leftList 和 rightList 中对应项目的属性
     */
    updateListsItem(globalIndex, property, value) {
      // 在 leftList 中查找并更新
      const leftItem = this.leftList.find(item => item.globalIndex === globalIndex);
      if (leftItem) {
        this.$set(leftItem, property, value);
      }

      // 在 rightList 中查找并更新
      const rightItem = this.rightList.find(item => item.globalIndex === globalIndex);
      if (rightItem) {
        this.$set(rightItem, property, value);
      }
    },
    getBoxHeight(leftList, rightList) {
      //获取左右两边高度
      return new Promise((resolve, reject) => {
        const indexComponent = this.$refs.indexComp;
        if (!indexComponent) {
          resolve();
          return;
        }

        // 先更新数据
        this.leftList = leftList;
        this.rightList = rightList;

        // 等待DOM更新后再查询
        this.$nextTick(() => {
          const query = uni.createSelectorQuery().in(indexComponent);
          query.select('#left').boundingClientRect();
          query.select('#right').boundingClientRect();
          query.exec(res => {
            if (res && res[0] && res[1]) {
              leftHeight = res[0].height; //获取左边列表的高度
              rightHeight = res[1].height; //获取右边列表的高度
            } else {
              leftHeight = 0;
              rightHeight = 0;
            }
            resolve();
          });
        });
      });
    },
    open_qr_code() {
      var info = this.getInfo;
      if (info['group_qrcode'] == null) {
        uni.showToast({
          title: '暂未设置',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.previewImage({
        current: info.group_qrcode,
        // 当前显示图片的http链接  
        urls: [info.group_qrcode]
      });
    },
    open_url(d) {
      uni.navigateTo({
        url: '/yl_welore/pages/packageA/article/index?id=' + d.currentTarget.dataset.id + '&type=' + d.currentTarget.dataset.key
      });
    },
    by_url(item) {
      var info = this.new_list[item.target.dataset.k];
      var videoContext = uni.createVideoContext('myVideo' + info['id']);
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        videoContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
      }
    },
    //投票
    vote_do(item) {
      var index = this.dian_index;
      var key = item.currentTarget.dataset.key;
      if (this.new_list[key].vo_id.length == 0) {
        uni.showToast({
          title: '请选择选项',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.paper_id = this.new_list[key].id;
      params.vo_id = this.agree(this.new_list[key].vo_id);
      var b = app.globalData.api_root + 'Polls/vote_do';
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == "success") {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.new_list[key].is_vo_check = 1;
            that.new_list[key].vo_count = that.new_list[key].vo_count + 1;
            that.new_list[key].vo[index].voters = that.new_list[key].vo[index].voters + 1;
            that.$forceUpdate(); // Forcing view update
          } else {
            uni.showModal({
              title: '提示',
              content: res.data.msg,
              showCancel: false
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    agree(rows) {
      var ids = [];
      for (var i = 0; i < rows.length; i++) {
        var signAgainReq = new Object();
        signAgainReq.pv_id = rows[i];
        ids.push(signAgainReq);
      }
      return JSON.stringify(ids);
    },
    //点击选项
    dian_option(item) {
      var index = item.currentTarget.dataset.index;
      this.dian_index = index;
      var key = item.currentTarget.dataset.key;
      var info = this.new_list[key];
      if (info.is_vo_check > 0) {
        return;
      }
      var vo = this.new_list[key].vo;
      var vo_id = item.currentTarget.dataset.id;
      var vo_id_list = this.new_list[key].vo_id;
      //console.log(index);
      //console.log(info.vo_id[index]);
      if (info['study_type'] == 4) {
        //单选
        if (vo_id == info['vo_id'][0]) {
          this.new_list[key].vo_id = [];
          this.$forceUpdate();
          return;
        }
        this.new_list[key].vo_id = [];
        this.new_list[key].vo_id = [vo_id];
        this.$forceUpdate();
      } else {
        var vo_id_index = vo_id_list.indexOf(vo_id);
        if (vo_id_list.indexOf(vo_id) != -1) {
          var vo_id_list_new = info.vo_id;
          vo_id_list_new.splice(vo_id_index, 1);
          this.new_list[key].vo_id = vo_id_list_new;
          this.$forceUpdate();
          return;
        } else {
          var vo_id_list_new = info.vo_id;
          vo_id_list_new.push(vo_id);
          this.new_list[key].vo_id = vo_id_list_new;
          this.$forceUpdate();
        }
      }
    },
    /**
     * 首页跳转链接
     */
    home_url(dd) {
      var warrant_arbor = getApp().globalData.store.getState().copyright['warrant_arbor'];
      var key = dd.currentTarget.dataset.k; //跳转类型
      if (key == 1) {
        //头像跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + dd.currentTarget.dataset.user_id
        });
        return;
      }
      if (key == 2) {
        //圈子跳转
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
        });
        return;
      }
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      if (key == 3) {
        //内容跳转
        var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
        var info = this.new_list[dd.currentTarget.dataset.index];
        if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
          uni.navigateTo({
            url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
          });
          return;
        }
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
        });
        return;
      }
    },
    getUrlkey(url) {
      var params = {};
      // console.log('1_分割url:', urls)
      var arr = [url];
      // console.log('2_分割urls[1]:', arr)
      for (var i = 0, l = arr.length; i < l; i++) {
        var a = arr[i].split("=");
        console.log('3_遍历 arr 并分割后赋值给a:', a[0], a[1]);
        params[a[0]] = a[1];
        console.log('4_a给params对象赋值:', params);
      }
      // console.log('5_结果:', params)
      return params;
    },
    doIt() {
      app.globalData.getLogin(
        // 成功回调 returnA 
        (userInfo) => {
          console.log(' 登录成功:', userInfo);
          this.uid = userInfo.id;
          var copyright = getApp().globalData.store.getState().copyright;
          if (Object.keys(copyright).length === 0) {
            this.authority();
          } else {
            this.copyright = copyright;
            app.globalData.store.setState({
              copyright: copyright
            });
          }
          if (this.copyright.tory_sort_arbor == 0) {
            this.add_trailing_list();
          } else {
            this.current_tj = 'tab2';
            this.get_tj_list();
          }
          this.get_placement_top();
          this.get_qq_info();
          this.get_subscribe();
        },
        // 失败回调 returnB 
        (err) => {
          console.error(' 登录失败:', err);
        }
      );
    },
    get_subscribe() {
      var subscribe = app.globalData.getCache("subscribe");
      console.log(subscribe);
      if (!subscribe) {
        app.globalData.subscribe_message(res => {
          //请求成功的回调函数
          console.log(res);
          if (res == '') {
            return;
          }
          app.globalData.setCache("subscribe", res.parallelism_data);
        }, () => {
          //请求失败的回调函数，不需要时可省略
        });
      } else {
        console.log('0');
      }
    },
    getLogin() {
      return new Promise((resolve, reject) => {
        var that = this;
        uni.login({
          success(res) {
            var params = new Object();
            params.code = res.code;
            http.POST(app.globalData.api_root + 'Login/index', {
              params: params,
              success: function (open) {
                console.log(open);
                var data = new Object();
                data.openid = open.data.info.openid;
                data.session_key = open.data.info.session_key;
                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                  params: data,
                  success: function (d) {
                    console.log(d);
                    app.globalData.setCache("userinfo", d.data.info);
                    resolve(d);
                    that.uid = d.data.info.uid;
                  }
                });
              }
            });
          }
        });
      });
    },
    /**
     * 点击话题
     */
    gambit_list(d) {
      var id = d.currentTarget.dataset.id;
      uni.navigateTo({
        url: '/yl_welore/pages/gambit/index?id=' + id
      });
    },
    //获取用户信息
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.user_info = res.data.info;
            that.version = res.data.info.version;
            that.admin = res.data.info.admin;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    handleCancel(e) {
      var user_id = e.currentTarget.dataset.user_id;
      var actions1 = this.actions1;
      var info = this.info;
      this.visible = true;
    },
    handleCancel1() {
      this.visible = false;
    },
    /**
     * 筛选操作
     */
    handleClickItem1(detail) {
      var index = detail.currentTarget.dataset.index;
      var actions = this.actions;
      var select = actions[index];
      console.log(select);
      this.visible = false;
      this.order_time = select['type'];
      this.actions_name = select['name'];
      var order = new Object();
      order.type = select['type'];
      order.name = select['name'];
      app.globalData.setCache("order_actions_o", order);
      var order_i = app.globalData.getCache("order_actions_o");
      this.page = 1;
      this.page_tj = 1;
      this.new_list = [];
      if (this.current_tj == 'tab1') {
        this.add_trailing_list();
      }
      if (this.current_tj == 'tab2') {
        this.get_tj_list();
      }
      if (this.current_tj == 'tab3') {
        this.get_video_list();
      }
      if (this.current_tj == 'tab4') {
        this.get_activity_list();
      }
      if (this.current_tj == 'tab5') {
        this.get_audio_list();
      }
    },
    preventTouchMove() { },
    handleChange({
      detail
    }) {
      this.current = detail.key;
    },
    handleChange_tg(detail) {
      var key = detail.currentTarget.dataset.key;
      this.page = 1;
      this.page_tj = 1;
      this.new_list = [];
      if (key == 'tab1') {
        this.add_trailing_list();
      }
      if (key == 'tab2') {
        this.get_tj_list();
      }
      if (key == 'tab3') {
        this.get_video_list();
      }
      if (key == 'tab4') {
        this.get_activity_list();
      }
      if (key == 'tab5') {
        this.get_audio_list();
      }
      this.current_tj = key;
    },
    handleChange_gz(detail) {
      console.log(detail);
      this.set_guanzhu = detail.currentTarget.dataset.key;
    },
    get_video_list() {
      var b = app.globalData.api_root + 'Index/get_video_list';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.page = that.page;
      var allMsg = that.new_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.new_list = allMsg;
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    get_activity_list() {
      var b = app.globalData.api_root + 'Index/get_activity_list';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.page = that.page;
      var allMsg = that.new_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.new_list = allMsg;
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    get_audio_list() {
      var b = app.globalData.api_root + 'Index/get_audio_list';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.page = that.page;
      var allMsg = that.new_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.new_list = allMsg;
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 首页推荐列表
     */
    get_tj_list() {
      var b = app.globalData.api_root + 'User/get_index_tj_list';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.index_tj_page = that.page_tj;
      var allMsg = that.new_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.new_list = allMsg;
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 获取圈子信息
     */
    get_qq_info() {
      var b = app.globalData.api_root + 'User/get_qq_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == 'success') {
            that.this_da_qq = res.data.info.this_da_qq;
            that.this_xiao_qq = res.data.info.this_xiao_qq;
            that.getInfo = res.data.info;
            that.title = res.data.info.realm_name;
            that.version = res.data.info.version;
            that.admin = res.data.info.admin;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 跳转
     */
    nav_add(d) {
      var e = app.globalData.getCache("userinfo");
      var that = this;
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var k = d.currentTarget.dataset.k;
      var diy = this.design;
      var copyright = this.copyright;
      if (copyright['force_phone_arbor'] == 1 && !e['user_phone'] && this.version == 0) {
        this.check_login();
        return;
      }
      var user_info = this.user_info;
      if (app.globalData.__CheckTheCertification(user_info)) {
        return;
      }
      // if(this.getInfo['release_count']!=0){
      //   if(this.getInfo['release_count']<=this.getInfo['check_today_terr']){
      //     wx.showToast({
      //       title: '每日限发帖次数：'+this.getInfo['release_count']+'次',
      //       icon: 'none',
      //       duration: 2000
      //     })
      //       return;
      //   }
      // }
      if (k == 'tuwen') {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
        });
      }
      if (k == 'toupiao') {
        //判断是否开启限制
        if (copyright['vote_member'] == 1) {
          if (this.getInfo['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=6&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'yuyin') {
        //判断是否开启限制
        if (copyright['voice_member'] == 1) {
          if (this.getInfo['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=1&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'shipin') {
        //判断是否开启限制
        if (copyright['video_member'] == 1) {
          if (this.getInfo['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=2&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
          });
        }
      }
      if (k == 'huodong') {
        //判断是否开启限制
        if (copyright['brisk_member'] == 1) {
          if (this.getInfo['vip'] == 1) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
            });
          } else {
            uni.showToast({
              title: 'VIP专属',
              icon: 'none',
              duration: 2000
            });
            return;
          }
        } else {
          uni.navigateTo({
            url: '/yl_welore/pages/packageA/add/index?type=4&fa_class=' + this.getInfo.id + '&name=' + this.getInfo.realm_name + '&gambit_name=&gambit_id=0'
          });
        }
      }
    },
    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.uid = e.uid;
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          that.mod = res.data.mod;
          that.add = res.data.pattern_data.release.list;
          that.design = res.data;
          app.globalData.store.setState({
            diy: res.data
          });
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 获取置顶帖子
     */
    get_placement_top() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.tory_id = this.id;
      var b = app.globalData.api_root + 'User/get_placement_top';
      http.POST(b, {
        params: params,
        success: function (res) {
          if (res.data.status == "success") {
            that.top_list = res.data.info;
            that.top_list_style = res.data.style;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 帖子数据
     */
    add_trailing_list() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      var list = that.getInfo;
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.index_page = this.page;
      var order = app.globalData.getCache("order_actions_o");
      if (!order) {
        params.order_time = this.order_time;
      } else {
        params.order_time = order['type'];
      }
      var b = app.globalData.api_root + 'User/get_index_list';
      var allMsg = that.new_list;
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == "success") {
            for (var i = 0; i < res.data.info.length; i++) {
              allMsg.push(res.data.info[i]);
            }
            that.new_list = allMsg;
            that.version = res.data.version;
            if (res.data.info.length == 0) {
              that.di_msg = true;
            }
            that.$nextTick(() => {
              that.isLeft();
            });
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 选择加入方式
     */
    set_trailing() {


      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      this.guanzhu = true;
    },
    hideModal() {
      this.guanzhu = false;
      this.check_phone_show = false;
      this.home_pl_check = false;
      this.purchase_paper_mod = false;
    },
    /**
    * 打开首页评论窗口
    */
    home_pl(d) {
      var checkLogin = app.globalData.checkPhoneLogin(1);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      uni.navigateTo({
        url: '/yl_welore/pages/packageA/article/index?id=' + d.currentTarget.dataset.id + '&type=' + d.currentTarget.dataset.type
      });
    },
    /**
     * 获取申请理由
     */
    get_gz_text_do(e) {
      this.get_gz_text = e.detail.value;
    },
    /**
     * 获取暗号
     */
    get_gz_input_do(e) {
      this.get_gz_input = e.detail.value;
    },
    /**
     * 加入圈子
     */
    add_trailing() {
      if (this.scene == '1154') {
        return;
      }
      var e = app.globalData.getCache("userinfo");
      var that = this;
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var params = new Object();
      var list = that.getInfo;
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      params.tory_id = this.id;
      params.is_trailing = list['is_trailing'] == true ? 1 : 0;
      //0申请1暗号
      params.trailing_type = this.set_guanzhu == 'tab1' ? 0 : 1;
      params.trailing_text = this.set_guanzhu == 'tab1' ? this.get_gz_text : this.get_gz_input;
      if (that.guanzhu == true) {
        if (!params.trailing_text) {
          uni.showToast({
            title: '内容不能为空',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }
      var b = app.globalData.api_root + 'User/set_user_trailing';
      http.POST(b, {
        params: params,
        success: function (res) {
          that.hideModal();
          if (res.data.status == "success") {
            // list['is_trailing'] = list['is_trailing'] == true ? false : true;
            // that.getInfo = list
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.get_tory_info();
            var subscribe = app.globalData.getCache("subscribe");
            if (subscribe && that.set_guanzhu == 'tab1' && subscribe['YL0005'] && subscribe['YL0008'] && subscribe['YL0009']) {
              app.globalData.authorization(subscribe['YL0005'], subscribe['YL0008'], subscribe['YL0009'], res => { });
            }
          } else {
            console.log(1);
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
          that.get_gz_text = '';
          that.get_gz_input = '';
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 圈子详情
     */
    get_tory_info() {
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = this.id;
      params.uid = e.uid;
      var b = app.globalData.api_root + 'User/get_tory_info';
      http.POST(b, {
        params: params,
        success: function (res) {
          console.log(res);
          if (res.data.status == "success") {
            that.getInfo = res.data.info;
            that.title = res.data.info.realm_name;
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    //点击弹出
    plus() {
      var that = this;
      var info = that.getInfo;
      var user_info = that.user_info;
      if (info['admin'] == 0 && info['this_da_qq'] == 0 && info['this_xiao_qq'] == 0) {
        if (user_info['level'] < info['release_level']) {
          uni.showToast({
            title: 'Lv.' + info['release_level'] + '等级才能发帖',
            icon: 'none',
            duration: 2000
          });
          return;
        }
      }
      console.log(that.isPopping);
      if (that.isPopping == false) {
        //弹出动画
        that.popp();
        that.isPopping = true;
      } else if (that.isPopping == true) {
        //缩回动画 
        that.takeback();
        that.isPopping = false;
      }
    },
    //弹出动画
    popp() {
      var that = this;
      //plus顺时针旋转
      var animationPlus = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect1 = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      animationPlus.step();
      var copyright = getApp().globalData.store.getState().copyright;
      var myCars = new Array();
      myCars[0] = [[25, -105], [25, -105]];
      myCars[1] = [[-50, -105], [100, -105]];
      myCars[2] = [[-85, -105], [30, -105], [140, -105]];
      myCars[3] = [[-100, -105], [-15, -105], [75, -105], [155, -105]];
      myCars[4] = [[-125, -105], [25, -105], [100, -105], [-50, -105], [170, -105]];
      var tab = [animationInput];
      if (copyright['version'] == 1) {
        //图文
      } else {
        //语音
        if (copyright['hair_audio_arbor'] == 1) {
          tab.push(animationTranspond);
        }
        //投票
        if (copyright['hair_vote_arbor'] == 1) {
          tab.push(animationcollect);
        }
        //视频
        if (copyright['hair_video_arbor'] == 1) {
          tab.push(animationM);
        }
        //活动
        if (copyright['hair_brisk_arbor'] == 1) {
          tab.push(animationHD);
        }
      }
      var key = tab.length - 1;
      for (var i = 0; i < tab.length; i++) {
        tab[i].translate(myCars[key][i][0], myCars[key][i][1]).opacity(1).height('50px').width('50px').step();
      }
      animBackCollect.backgroundColor('#F7F9FA').height(190).step();
      that.animPlus = animationPlus.export();
      that.animCollect = animationcollect.export();
      that.animTranspond = animationTranspond.export();
      that.animInput = animationInput.export();
      that.animationM = animationM.export();
      that.animBack = animBackCollect.export();
      that.animationHD = animationHD.export();
    },
    //收回动画
    takeback() {
      var that = this;
      //plus逆时针旋转
      var animationPlus = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationcollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationTranspond = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationInput = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animBackCollect = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationM = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      var animationHD = uni.createAnimation({
        duration: 500,
        timingFunction: 'ease-out'
      });
      animationPlus.rotateZ(0).step();
      animationcollect.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationTranspond.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationInput.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationM.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animationHD.translate(0, 0).rotateZ(0).opacity(0).height('0rpx').width('0rpx').step();
      animBackCollect.backgroundColor('transparent').height(45).step();
      that.animPlus = animationPlus.export();
      that.animCollect = animationcollect.export();
      that.animTranspond = animationTranspond.export();
      that.animInput = animationInput.export();
      that.animationM = animationM.export();
      that.animBack = animBackCollect.export();
      that.animationHD = animationHD.export();
      this.get_user_info();
    },
    //播放声音
    play(e) {
      var that = this;
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      var key = 1;
      var info = this.new_list[index];
      if (info['check_look'] == 0 && info['is_buy'] > 0) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      uni.getBackgroundAudioPlayerState({
        success(res) {
          console.log(res);
          const status = res.status;
          key = res.status;
        }
      });
      for (var i = 0; i < nuw.length; i++) {
        nuw[i]['is_voice'] = false;
      }
      this.new_list = nuw;
      console.log('播放');
      innerAudioContext.src = e.currentTarget.dataset.vo;
      innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
      innerAudioContext.onTimeUpdate(() => {
        //console.log(innerAudioContext.currentTime)
        var duration = innerAudioContext.duration;
        var offset = innerAudioContext.currentTime;
        var currentTime = parseInt(innerAudioContext.currentTime);
        var min = "0" + parseInt(currentTime / 60);
        var sec = currentTime % 60;
        if (sec < 10) {
          sec = "0" + sec;
        }
        ;
        var starttime = min + ':' + sec; /*  00:00  */

        nuw[index]['starttime'] = starttime;
        nuw[index]['offset'] = offset;
        that.new_list = nuw;
        that.$forceUpdate();
      });
      // innerAudioContext.play();

      nuw[index]['is_voice'] = true;
      this.new_list = nuw;
      this.new_list_index = index;
      //播放结束
      innerAudioContext.onEnded(() => {
        var nuw = this.new_list;
        nuw[index]['is_voice'] = false;
        that.starttime = '00:00';
        that.offset = 0;
        that.new_list = nuw;
        that.$forceUpdate();
        console.log("音乐播放结束");
      });
      innerAudioContext.play();
    },
    /**
     * 停止
     */
    stop(e) {
      innerAudioContext.pause();
      console.log('暂停');
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      nuw[index]['is_voice'] = false;
      this.new_list = nuw;
    },
    // 进度条拖拽
    sliderChange(e) {
      var that = this;
      var index = e.currentTarget.dataset.key;
      var nuw = this.new_list;
      var info = this.new_list[index];
      if (info['check_look'] == 0 && info['is_buy'] == 1) {
        innerAudioContext.stop();
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/article/index?id=' + info['id'] + '&type=' + info['study_type']
        });
        return;
      }
      var offset = parseInt(e.detail.value);
      innerAudioContext.play();
      innerAudioContext.seek(offset);
      var nuw = this.new_list;
      nuw[index]['is_voice'] = true;
      this.new_list = nuw;
    },
    /**
     * 点赞
     */
    add_top_zan(data) {
      if (this.scene == '1154') {
        return;
      }
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.zan_type = this.top_list[key]['is_info_zan'] == true ? 1 : 0;
      var list = that.top_list;
      uni.vibrateShort();
      if (list[key]['is_info_zan'] == false) {
        list[key]['is_info_zan'] = true;
      } else {
        list[key]['is_info_zan'] = false;
      }
      that.top_list = list;
      that.rotate3d_top(key);
      params.applaud_type = 0;
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: function (res) {
          var list = that.top_list;
          if (res.data.status == "success") {
            //list[key]['is_info_zan'] = res.data.info_zan;
            list[key]['info_zan_count'] = res.data.info_zan_count;
            that.top_list = list;
            that.$forceUpdate();
            // that.rotate3d(key);
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 点赞
     */
    add_zan(data) {
      if (this.scene == '1154') {
        return;
      }
      var e = app.globalData.getCache("userinfo");
      var that = this;
      var checkLogin = app.globalData.checkPhoneLogin(0);
      if (checkLogin == 1) {
        this.check_user_login = true;
        return;
      }
      if (checkLogin == 2) {
        this.check_phone_show = true;
        return;
      }
      var id = data.currentTarget.dataset.id;
      var key = data.currentTarget.dataset.key;
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.id = id;
      params.uid = e.uid;
      params.zan_type = this.new_list[key]['is_info_zan'] == true ? 1 : 0;
      var list = that.new_list;
      uni.vibrateShort();
      if (list[key]['is_info_zan'] == false) {
        // 先计算新的值
        const newZanStatus = true;
        const newZanCount = list[key]['info_zan_count_this'] + 1;

        // 同时更新所有数据源
        this.$set(this.new_list[key], 'is_info_zan', newZanStatus);
        this.$set(this.new_list[key], 'info_zan_count_this', newZanCount);
        this.updateListsItem(key, 'is_info_zan', newZanStatus);
        this.updateListsItem(key, 'info_zan_count_this', newZanCount);
      } else {
        // 先计算新的值
        const newZanStatus = false;
        const newZanCount = list[key]['info_zan_count_this'] - 1 < 0 ? 0 : list[key]['info_zan_count_this'] - 1;

        // 同时更新所有数据源
        this.$set(this.new_list[key], 'is_info_zan', newZanStatus);
        this.$set(this.new_list[key], 'info_zan_count_this', newZanCount);
        this.updateListsItem(key, 'is_info_zan', newZanStatus);
        this.updateListsItem(key, 'info_zan_count_this', newZanCount);
      }

      // if (list[key]['is_info_zan'] == false) {
      //   list[key]['is_info_zan'] = true;
      // } else {
      //   list[key]['is_info_zan'] = false;
      // }

      // that.new_list = list
      that.rotate3d(key);
      params.applaud_type = 0;
      var b = app.globalData.api_root + 'User/add_user_zan';
      http.POST(b, {
        params: params,
        success: function (res) {
          var list = that.new_list;
          if (res.data.status == "success") {

            //list[key]['is_info_zan'] = res.data.info_zan;
            //list[key]['info_zan_count'] = res.data.info_zan_count;

            // that.new_list = list
            // that.rotate3d(key);
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
    /**
     * 打开地图
     */
    get_position(d) {
      var a = Number(d.currentTarget.dataset.latitude);
      var o = Number(d.currentTarget.dataset.longitude);
      var name = d.currentTarget.dataset.pos_name;
      if (a && o) {
        uni.openLocation({
          latitude: a,
          longitude: o,
          name: name
        });
      }
    },
    /**
     * 动画
     */
    rotate3d_top(key) {
      var that = this;
      var list = that.top_list;
      // 创建一个动画实例
      var animation_zan = uni.createAnimation({
        // 动画持续时间
        duration: 300,
        // 定义动画效果，当前是匀速
        timingFunction: 'ease'
      });
      // 将该变量赋值给当前动画
      that.animation_zan = animation_zan;
      // 使用缩放动画替代旋转，避免图标反转

      animation_zan.scale(1.2).step();
      list[key]['animationData_zan'] = animation_zan.export();
      that.top_list = list;
      that.$forceUpdate();
      setTimeout(function () {
        var list_g = that.top_list;
        animation_zan.scale(1).step();
        list_g[key]['animationData_zan'] = animation_zan.export();
        that.top_list = list_g;
        that.$forceUpdate();
      }, 150);
    },
    /**
     * 是否授权
     */
    url_is_b() {
      var e = app.globalData.getCache("userinfo");
      console.log(e);
      var that = this;
      if (e.tourist == 0) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/circle_info/index?id=' + that.id
        });
        return;
      }
    },
    /**
     * 动画
     */
    rotate3d(key) {
      var that = this;
      var list = that.new_list;
      // 创建一个动画实例
      var animation_zan = uni.createAnimation({
        // 动画持续时间
        duration: 300,
        // 定义动画效果，当前是匀速
        timingFunction: 'ease'
      });
      // 将该变量赋值给当前动画
      that.animation_zan = animation_zan;
      // 使用缩放动画替代旋转，避免图标反转

      animation_zan.scale(1.2).step();
      list[key]['animationData_zan'] = animation_zan.export();
      that.new_list = list;
      that.$forceUpdate();
      setTimeout(function () {
        var list_g = that.new_list;
        animation_zan.scale(1).step();
        list_g[key]['animationData_zan'] = animation_zan.export();
        that.new_list = list_g;
        that.$forceUpdate();
      }, 150);
    },
    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_authority';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: function (res) {
          that.copyright = res.data;
          that.version = res.data.version;
          app.globalData.store.setState({
            copyright: res.data
          });
        },
        fail: function () {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: function (res) { }
          });
        }
      });
    },
  }
};
</script>
<style>
page {
  background-color: #fff;

}

/* 置顶内容区域样式 */
.top-content-container {
  position: relative;
  z-index: 999;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  margin-top: -10px;
  border-radius: 20rpx 20rpx 0 0;
  color: #000;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.top-item-wrapper {
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}


.top-item-wrapper:last-child {
  border-bottom: none;
}

.top-badge {
  display: flex;
  align-items: center;
  background: #4169E1;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 10px rgba(173, 216, 230, 0.3);
  margin-right: 20rpx;
  flex-shrink: 0;
}

.top-icon {
  margin-right: 6rpx;
  font-size: 14px;
}

.top-label {
  font-size: 12px;
  font-weight: 600;
}

.top-content-text {
  flex: 1;
  font-size: 15px;
  color: #333;
  line-height: 1.5;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.top-arrow {
  margin-left: 15rpx;
  color: #999;
  font-size: 14px;
  transition: all 0.3s ease;
}

.top-divider-line {
  height: 8px;
  background: linear-gradient(90deg, #f1f3f4 0%, #e8eaed 50%, #f1f3f4 100%);
  width: 100%;
  position: relative;
}

.top-divider-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

/* 标签页导航样式 - 简洁设计 */
.tab-navigation-container {
  background: transparent;
  padding: 20rpx 0;
  position: relative;
}

.tab-navigation-container::before {
  display: none;
}

.tab-scroll-wrapper {
  display: flex;
  padding: 0 30rpx;
  overflow-x: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  position: relative;
  z-index: 1;
  gap: 12rpx;
}

.tab-scroll-wrapper::-webkit-scrollbar {
  display: none;
}

.tab-item-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 28rpx;
  border-radius: 40rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  cursor: pointer;
  position: relative;
  min-width: 120rpx;
  flex-shrink: 0;
}

.tab-item-modern:active {
  transform: translateY(0);
}

.tab-active-modern {
  background: linear-gradient(135deg, #ADD8E6 0%, #4169E1 100%);
  border-color: #ADD8E6;
  box-shadow: 0 6rpx 20rpx rgba(173, 216, 230, 0.3);
}


.tab-icon {
  font-size: 16px;
  margin-right: 8rpx;
  transition: transform 0.3s ease;
}

.tab-active-modern .tab-icon {
  transform: scale(1.1);
}

.tab-text-modern {
  font-size: 28rpx;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  white-space: nowrap;
  letter-spacing: 0.5rpx;
}

.tab-active-modern .tab-text-modern {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.tab-active-modern::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg, #ADD8E6 0%, #4169E1 100%);
  border-radius: 42rpx;
  z-index: -1;
  opacity: 0.1;
}

.tab-active-modern::after {
  content: '';
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  width: 40%;
  height: 3rpx;
  background: linear-gradient(90deg, transparent 0%, #ADD8E6 50%, transparent 100%);
  border-radius: 2rpx;
  transform: translateX(-50%);
  opacity: 0.8;
  animation: tab-glow 2s ease-in-out infinite alternate;
}

@keyframes tab-glow {
  0% {
    opacity: 0.6;
    width: 30%;
  }
  100% {
    opacity: 1;
    width: 50%;
  }
}

/* 排序选择弹窗样式 */
.sort-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sort-modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.sort-modal-dialog {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 25rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sort-modal-overlay.show .sort-modal-dialog {
  transform: translateY(0);
}

.sort-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sort-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sort-modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}


.sort-modal-close text {
  color: white;
  font-size: 16px;
}

.sort-options-list {
  padding: 20rpx 0;
  max-height: 60vh;
  overflow-y: auto;
}

.sort-option-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 40rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.02);
  position: relative;
}


.sort-option-item:last-child {
  border-bottom: none;
}

.sort-option-active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-left: 4px solid #667eea;
}

.sort-option-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.sort-option-icon {
  font-size: 20px;
  margin-right: 20rpx;
}

.sort-option-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.sort-option-active .sort-option-text {
  color: #667eea;
  font-weight: 600;
}

.sort-option-check {
  color: #667eea;
  font-size: 20px;
  animation: checkBounce 0.3s ease;
}

@keyframes checkBounce {
  0% {
    transform: scale(0);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.now_level {
  width: 0px;
  height: 0px;
}

.nav-wrap {
  width: 100%;
  top: 0;
  background: #fff;
  color: #000;
  z-index: 888;
  height: 200px;
}

.padd_padd {
  padding-bottom: 20px;
}

.pinglunzan {
  margin-top: 20rpx;
}

.classify {
  height: 100%;
  width: 70%;
  margin: 0 auto;
  text-align: center;
  background-color: #fff;
  box-sizing: border-box;
  position: relative;
}

.img-plus-style {
  height: 100rpx;
  width: 100rpx;
  right: 42%;
  position: absolute;
  z-index: 100;
  bottom: 20px;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.load {
  width: 100% !important;
  margin: 0 !important;
  line-height: normal !important;
  font-size: 14px;
  text-align: center;
}


.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  z-index: 500;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.zhiding_gg {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  font-size: 14px;
}

.yes_pos {
  width: 13.5% !important;
}

.weui-tabbar {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  z-index: 500;
  bottom: 0;
  width: 100%;
  background-color: #f7f7fa;
}

button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
  overflow: inherit;
}


/**index.wxss**/
.audiosBox {
  width: 92%;
  margin: auto;
  height: 130rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f6f7f7;
  border-radius: 10rpx;
}

/*按钮大小  */
.audioOpen {
  width: 50rpx;
  height: 50rpx;
  border: 1px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-top: 57rpx;
}

.image2 {
  margin-left: 10%;
}

/*进度条长度  */
.slid {
  flex: 1;
  position: relative;
}

.slid view {
  display: flex;
  justify-content: space-between;
}

.slid view>text:nth-child(1) {
  color: #4c9dee;
  margin-left: 6rpx;
}

.slid view>text:nth-child(2) {
  margin-right: 6rpx;
}

slider {
  width: 520rpx;
  margin: 0;
  margin-left: 35rpx;
}

/*横向布局  */
.times {
  width: 100rpx;
  text-align: center;
  display: inline-block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 5rpx;
}

.title view {
  text-indent: 2em;
}

.stars {
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: 990px 1462px #FFF, 1859px 1557px #FFF, 666px 1823px #FFF, 622px 276px #FFF, 1421px 742px #FFF, 1118px 976px #FFF, 1569px 1407px #FFF, 794px 166px #FFF, 1782px 1475px #FFF, 1966px 1426px #FFF, 96px 1642px #FFF, 61px 376px #FFF, 770px 287px #FFF, 158px 356px #FFF, 228px 985px #FFF, 195px 1641px #FFF, 1731px 1517px #FFF, 436px 732px #FFF, 1184px 1458px #FFF, 1361px 558px #FFF, 1004px 947px #FFF, 1311px 1219px #FFF, 1568px 340px #FFF, 913px 1804px #FFF, 1952px 1924px #FFF, 531px 738px #FFF, 1846px 268px #FFF, 248px 184px #FFF, 86px 823px #FFF, 437px 1390px #FFF, 1660px 443px #FFF, 644px 702px #FFF, 1052px 1764px #FFF, 1353px 1122px #FFF, 1170px 425px #FFF, 434px 479px #FFF, 965px 765px #FFF, 667px 1749px #FFF, 1823px 1439px #FFF, 1287px 823px #FFF, 1998px 1068px #FFF, 409px 1087px #FFF, 1312px 1535px #FFF, 111px 519px #FFF, 490px 1497px #FFF, 186px 1544px #FFF, 1508px 1324px #FFF, 1184px 732px #FFF, 1150px 1759px #FFF, 25px 908px #FFF, 396px 161px #FFF, 51px 267px #FFF, 1160px 1256px #FFF, 1722px 8px #FFF, 334px 287px #FFF, 275px 902px #FFF, 1064px 806px #FFF, 140px 435px #FFF, 453px 1272px #FFF, 933px 1709px #FFF, 1446px 1563px #FFF, 182px 1721px #FFF, 1172px 1510px #FFF, 1744px 1086px #FFF, 233px 1233px #FFF, 137px 1430px #FFF, 297px 141px #FFF, 1654px 218px #FFF, 1111px 1701px #FFF, 333px 468px #FFF, 766px 1221px #FFF, 687px 649px #FFF, 555px 705px #FFF, 544px 660px #FFF, 1679px 815px #FFF, 448px 1697px #FFF, 704px 376px #FFF, 599px 822px #FFF, 1233px 1926px #FFF, 1439px 152px #FFF, 215px 1079px #FFF, 1096px 834px #FFF, 817px 594px #FFF, 160px 1486px #FFF, 248px 1442px #FFF, 1424px 895px #FFF, 733px 887px #FFF, 1100px 557px #FFF, 948px 569px #FFF, 1441px 1642px #FFF, 1651px 1547px #FFF, 601px 22px #FFF, 306px 11px #FFF, 612px 1615px #FFF, 1432px 1564px #FFF, 1073px 1683px #FFF, 1926px 137px #FFF, 786px 98px #FFF, 1346px 1262px #FFF, 574px 763px #FFF, 989px 1852px #FFF, 1188px 1660px #FFF, 726px 355px #FFF, 1574px 814px #FFF, 759px 1729px #FFF, 644px 286px #FFF, 373px 1272px #FFF, 837px 1106px #FFF, 1321px 819px #FFF, 1094px 1654px #FFF, 1194px 1674px #FFF, 363px 363px #FFF, 470px 1092px #FFF, 1254px 393px #FFF, 1727px 1958px #FFF, 1402px 682px #FFF, 379px 489px #FFF, 1815px 308px #FFF, 972px 890px #FFF, 1296px 1577px #FFF, 1678px 283px #FFF, 1760px 91px #FFF, 1764px 578px #FFF, 1319px 822px #FFF, 725px 1436px #FFF, 77px 1971px #FFF, 220px 1985px #FFF, 1151px 334px #FFF, 541px 279px #FFF, 39px 345px #FFF, 673px 643px #FFF, 777px 384px #FFF, 1876px 1788px #FFF, 810px 1329px #FFF, 57px 1829px #FFF, 1751px 1027px #FFF, 951px 467px #FFF, 1842px 1100px #FFF, 990px 1585px #FFF, 1907px 815px #FFF, 4px 1695px #FFF, 1439px 1075px #FFF, 1355px 1455px #FFF, 1279px 1706px #FFF, 986px 995px #FFF, 1082px 1913px #FFF, 897px 1031px #FFF, 1606px 549px #FFF, 758px 1608px #FFF, 340px 1221px #FFF, 1564px 1728px #FFF, 1097px 836px #FFF, 41px 762px #FFF, 1956px 377px #FFF, 1859px 157px #FFF, 309px 413px #FFF, 768px 675px #FFF, 693px 673px #FFF, 464px 467px #FFF, 1956px 683px #FFF, 1294px 1206px #FFF, 471px 1110px #FFF, 1877px 1816px #FFF, 1310px 404px #FFF, 489px 59px #FFF, 84px 540px #FFF, 78px 54px #FFF, 373px 1667px #FFF, 1317px 775px #FFF, 386px 361px #FFF, 667px 323px #FFF, 1987px 664px #FFF, 66px 303px #FFF, 1394px 846px #FFF, 1181px 1901px #FFF, 303px 1351px #FFF, 1630px 204px #FFF, 1199px 667px #FFF, 1003px 1924px #FFF, 839px 1583px #FFF, 103px 756px #FFF, 1561px 841px #FFF, 1166px 1136px #FFF, 899px 913px #FFF, 555px 465px #FFF, 1589px 93px #FFF, 546px 851px #FFF, 1603px 570px #FFF, 832px 1322px #FFF, 665px 1284px #FFF, 1092px 1237px #FFF, 626px 1893px #FFF, 1987px 983px #FFF, 865px 669px #FFF, 1586px 296px #FFF, 938px 415px #FFF, 1289px 1217px #FFF, 1079px 1904px #FFF, 1324px 1386px #FFF, 665px 1482px #FFF, 775px 622px #FFF, 1195px 530px #FFF, 1408px 699px #FFF, 1315px 1px #FFF, 1385px 1359px #FFF, 1328px 534px #FFF, 855px 7px #FFF, 1436px 25px #FFF, 1376px 199px #FFF, 39px 534px #FFF, 1136px 1635px #FFF, 909px 1584px #FFF, 895px 6px #FFF, 903px 308px #FFF, 1914px 1534px #FFF, 718px 1073px #FFF, 415px 53px #FFF, 1816px 119px #FFF, 1747px 1133px #FFF, 891px 352px #FFF, 186px 1626px #FFF, 1296px 1128px #FFF, 1328px 937px #FFF, 1486px 853px #FFF, 342px 346px #FFF, 284px 1156px #FFF, 376px 28px #FFF, 607px 1105px #FFF, 1325px 301px #FFF, 881px 1185px #FFF, 381px 1214px #FFF, 465px 254px #FFF, 1922px 1784px #FFF, 508px 1805px #FFF, 1403px 797px #FFF, 1024px 1918px #FFF, 1532px 1310px #FFF, 858px 810px #FFF, 208px 734px #FFF, 1543px 1848px #FFF, 1124px 344px #FFF, 546px 1467px #FFF, 1092px 307px #FFF, 282px 1054px #FFF, 903px 1625px #FFF, 279px 1698px #FFF, 1097px 1619px #FFF, 1438px 359px #FFF, 1266px 152px #FFF, 352px 1515px #FFF, 1855px 1781px #FFF, 293px 1924px #FFF, 999px 1688px #FFF, 387px 350px #FFF, 1220px 887px #FFF, 457px 510px #FFF, 1629px 587px #FFF, 832px 675px #FFF, 270px 9px #FFF, 771px 157px #FFF, 1559px 578px #FFF, 308px 1328px #FFF, 994px 1230px #FFF, 1059px 19px #FFF, 119px 367px #FFF, 386px 759px #FFF, 1731px 715px #FFF, 1798px 759px #FFF, 507px 559px #FFF, 1388px 1892px #FFF, 1972px 1655px #FFF, 675px 612px #FFF, 1615px 1431px #FFF, 1157px 1623px #FFF, 932px 229px #FFF, 1077px 632px #FFF, 1670px 330px #FFF, 1373px 879px #FFF, 1971px 363px #FFF, 1681px 1292px #FFF, 1015px 1318px #FFF, 755px 895px #FFF, 1945px 328px #FFF, 557px 1984px #FFF, 1755px 748px #FFF, 186px 175px #FFF, 780px 1830px #FFF, 396px 660px #FFF, 305px 192px #FFF, 1902px 628px #FFF, 658px 1463px #FFF, 1856px 1463px #FFF, 1628px 366px #FFF, 1793px 917px #FFF, 1129px 964px #FFF, 276px 386px #FFF, 1933px 1855px #FFF, 1257px 445px #FFF, 1145px 1434px #FFF, 1890px 130px #FFF, 1246px 1710px #FFF, 1544px 1119px #FFF, 556px 1422px #FFF, 1999px 1721px #FFF, 305px 1609px #FFF, 1124px 483px #FFF, 1278px 812px #FFF, 109px 1174px #FFF, 1690px 1726px #FFF, 1251px 1704px #FFF, 1130px 1270px #FFF, 1066px 1904px #FFF, 788px 831px #FFF, 789px 185px #FFF, 222px 286px #FFF, 660px 1593px #FFF, 1229px 873px #FFF, 1690px 299px #FFF, 965px 500px #FFF, 1376px 1061px #FFF, 378px 207px #FFF, 908px 1157px #FFF, 182px 232px #FFF, 43px 1660px #FFF, 1444px 1165px #FFF, 1526px 1381px #FFF, 944px 434px #FFF, 897px 1765px #FFF, 1784px 1335px #FFF, 1290px 1041px #FFF, 116px 1246px #FFF, 1082px 883px #FFF, 1816px 1636px #FFF, 1493px 1953px #FFF, 904px 566px #FFF, 621px 716px #FFF, 275px 1951px #FFF, 1310px 119px #FFF, 1378px 370px #FFF, 22px 902px #FFF, 565px 1340px #FFF, 1588px 1644px #FFF, 1313px 1933px #FFF, 550px 1049px #FFF, 1421px 313px #FFF, 1272px 1851px #FFF, 1359px 1859px #FFF, 1641px 1674px #FFF, 1546px 286px #FFF, 1946px 1570px #FFF, 491px 308px #FFF, 606px 380px #FFF, 361px 1317px #FFF, 1396px 477px #FFF, 1864px 1385px #FFF, 182px 1379px #FFF, 75px 33px #FFF, 990px 880px #FFF, 770px 111px #FFF, 1115px 1633px #FFF, 1527px 160px #FFF, 1867px 719px #FFF, 1046px 442px #FFF, 79px 47px #FFF, 143px 1764px #FFF, 896px 1182px #FFF, 1401px 585px #FFF, 1151px 783px #FFF, 476px 1906px #FFF, 589px 1741px #FFF, 306px 1731px #FFF, 779px 1670px #FFF, 144px 1362px #FFF, 1280px 819px #FFF, 328px 874px #FFF, 1420px 306px #FFF, 1445px 1660px #FFF, 1359px 860px #FFF, 181px 399px #FFF, 1488px 1989px #FFF, 964px 1927px #FFF, 164px 1302px #FFF, 378px 1642px #FFF, 1862px 552px #FFF, 1550px 1654px #FFF, 451px 915px #FFF, 243px 904px #FFF, 730px 1137px #FFF, 1793px 718px #FFF, 1026px 1272px #FFF, 1142px 1454px #FFF, 973px 1526px #FFF, 211px 1041px #FFF, 1938px 1731px #FFF, 39px 1077px #FFF, 80px 886px #FFF, 266px 1699px #FFF, 926px 648px #FFF, 1024px 596px #FFF, 1957px 1197px #FFF, 1870px 22px #FFF, 1031px 1835px #FFF, 1445px 70px #FFF, 1858px 1437px #FFF, 1240px 1176px #FFF, 993px 1068px #FFF, 334px 1315px #FFF, 1016px 182px #FFF, 594px 689px #FFF, 1347px 855px #FFF, 1054px 512px #FFF, 59px 1512px #FFF, 935px 1057px #FFF, 1606px 1483px #FFF, 1541px 809px #FFF, 951px 498px #FFF, 813px 265px #FFF, 1767px 628px #FFF, 69px 559px #FFF, 861px 1494px #FFF, 248px 1326px #FFF, 1763px 1762px #FFF, 1090px 1934px #FFF, 574px 350px #FFF, 254px 1789px #FFF, 664px 784px #FFF, 425px 1347px #FFF, 779px 724px #FFF, 1315px 756px #FFF, 163px 709px #FFF, 625px 379px #FFF, 765px 689px #FFF, 536px 1556px #FFF, 1000px 1633px #FFF, 1443px 417px #FFF, 208px 1490px #FFF, 13px 575px #FFF, 1534px 1001px #FFF, 1086px 450px #FFF, 190px 136px #FFF, 198px 1518px #FFF, 949px 714px #FFF, 567px 1140px #FFF, 1897px 979px #FFF, 299px 160px #FFF, 331px 234px #FFF, 1452px 1890px #FFF, 1165px 1564px #FFF, 599px 202px #FFF, 178px 1536px #FFF, 1101px 770px #FFF, 1700px 1336px #FFF, 1865px 416px #FFF, 1115px 1335px #FFF, 1814px 1876px #FFF, 62px 964px #FFF, 499px 302px #FFF, 1778px 1640px #FFF, 1954px 939px #FFF, 815px 1726px #FFF, 551px 823px #FFF, 980px 1670px #FFF, 1643px 220px #FFF, 1653px 1098px #FFF, 1081px 1178px #FFF, 1102px 1371px #FFF, 1113px 227px #FFF, 1132px 1926px #FFF, 1959px 307px #FFF, 1178px 1277px #FFF, 980px 13px #FFF, 565px 1196px #FFF, 557px 1133px #FFF, 106px 4px #FFF, 314px 1690px #FFF, 677px 1947px #FFF, 212px 853px #FFF, 1161px 602px #FFF, 481px 522px #FFF, 1112px 140px #FFF, 821px 446px #FFF, 249px 1779px #FFF, 1096px 869px #FFF, 1890px 634px #FFF, 826px 372px #FFF, 928px 1065px #FFF, 976px 414px #FFF, 1493px 1918px #FFF, 1299px 845px #FFF, 1988px 103px #FFF, 538px 1234px #FFF, 88px 559px #FFF, 1597px 1849px #FFF, 541px 271px #FFF, 1899px 859px #FFF, 775px 757px #FFF, 841px 500px #FFF, 482px 1776px #FFF, 753px 1377px #FFF, 1912px 1382px #FFF, 1858px 966px #FFF, 191px 1364px #FFF, 774px 1425px #FFF, 1917px 1101px #FFF, 740px 1554px #FFF, 1401px 619px #FFF, 263px 877px #FFF, 1108px 1483px #FFF, 1868px 1236px #FFF, 1490px 1595px #FFF, 1313px 735px #FFF, 299px 676px #FFF, 239px 1814px #FFF, 1741px 11px #FFF, 498px 731px #FFF, 337px 243px #FFF, 1798px 253px #FFF, 598px 479px #FFF, 1361px 1621px #FFF, 1925px 1499px #FFF, 521px 754px #FFF, 346px 1594px #FFF, 1337px 1893px #FFF, 814px 1903px #FFF, 1422px 622px #FFF, 1669px 958px #FFF, 913px 780px #FFF, 219px 518px #FFF, 1232px 1915px #FFF, 1560px 110px #FFF, 194px 234px #FFF, 94px 1462px #FFF, 503px 815px #FFF, 1546px 524px #FFF, 155px 1481px #FFF, 1719px 1118px #FFF, 1744px 1972px #FFF, 1445px 1382px #FFF, 1701px 488px #FFF, 1010px 551px #FFF, 1927px 613px #FFF, 1809px 1925px #FFF, 1757px 1804px #FFF, 1916px 537px #FFF, 1098px 1589px #FFF, 78px 1608px #FFF, 1333px 288px #FFF, 60px 1437px #FFF, 1003px 1111px #FFF, 1058px 1238px #FFF, 672px 1723px #FFF, 157px 619px #FFF, 448px 1755px #FFF, 502px 1143px #FFF, 34px 1732px #FFF, 1951px 657px #FFF, 244px 1012px #FFF, 1679px 658px #FFF, 603px 318px #FFF, 1435px 353px #FFF, 1147px 706px #FFF, 1318px 1887px #FFF, 1115px 292px #FFF, 538px 1843px #FFF, 709px 315px #FFF, 47px 987px #FFF, 42px 845px #FFF, 196px 3px #FFF, 1583px 1107px #FFF, 459px 137px #FFF, 45px 1458px #FFF, 314px 391px #FFF, 1433px 1142px #FFF, 1240px 901px #FFF, 716px 52px #FFF, 1550px 1576px #FFF, 495px 590px #FFF, 155px 1979px #FFF, 1647px 1226px #FFF, 576px 12px #FFF, 73px 240px #FFF, 1631px 1775px #FFF, 223px 960px #FFF, 1193px 696px #FFF, 1231px 1285px #FFF, 633px 735px #FFF, 419px 409px #FFF, 1803px 832px #FFF, 691px 194px #FFF, 1920px 1169px #FFF, 1299px 870px #FFF, 1859px 1582px #FFF, 617px 1021px #FFF, 236px 153px #FFF, 485px 1630px #FFF, 1394px 433px #FFF, 294px 1475px #FFF, 1759px 353px #FFF, 18px 760px #FFF, 1811px 1169px #FFF, 1836px 1673px #FFF, 1247px 1961px #FFF, 1231px 415px #FFF, 1138px 957px #FFF, 253px 928px #FFF, 1227px 1631px #FFF, 594px 1523px #FFF, 1295px 75px #FFF, 1277px 901px #FFF, 399px 1106px #FFF, 69px 62px #FFF, 146px 1366px #FFF, 1796px 706px #FFF, 1786px 56px #FFF, 1678px 108px #FFF, 746px 403px #FFF, 1892px 1497px #FFF, 1011px 1948px #FFF, 454px 1322px #FFF, 1765px 1506px #FFF, 373px 358px #FFF, 131px 1817px #FFF, 41px 259px #FFF, 63px 1250px #FFF, 1382px 1867px #FFF, 569px 1870px #FFF, 763px 388px #FFF, 149px 114px #FFF, 1490px 736px #FFF, 817px 1258px #FFF, 1833px 717px #FFF, 980px 763px #FFF, 823px 483px #FFF, 1522px 1401px #FFF, 1px 1674px #FFF, 1752px 1897px #FFF, 1813px 1770px #FFF, 135px 1735px #FFF, 277px 569px #FFF, 1410px 459px #FFF, 600px 386px #FFF, 1104px 1708px #FFF, 1081px 661px #FFF, 645px 715px #FFF, 220px 1948px #FFF, 1976px 39px #FFF, 1541px 1945px #FFF, 745px 207px #FFF, 1333px 1096px #FFF, 18px 1285px #FFF, 1134px 504px #FFF, 511px 1234px #FFF, 708px 1908px #FFF, 1121px 571px #FFF, 1686px 1872px #FFF, 382px 214px #FFF, 923px 1699px #FFF, 1471px 533px #FFF, 991px 268px #FFF, 81px 1152px #FFF, 1922px 180px #FFF, 90px 1341px #FFF, 974px 1025px #FFF, 811px 401px #FFF, 1200px 1573px #FFF, 197px 285px #FFF, 349px 1838px #FFF, 523px 647px #FFF, 1562px 1535px #FFF, 1243px 498px #FFF, 268px 1536px #FFF, 775px 1772px #FFF, 751px 157px #FFF, 524px 710px #FFF, 373px 971px #FFF, 290px 1377px #FFF, 1121px 128px #FFF, 323px 1678px #FFF, 897px 473px #FFF, 675px 1581px #FFF, 1279px 1605px #FFF, 1161px 316px #FFF, 1647px 1055px #FFF, 1480px 176px #FFF, 664px 621px #FFF, 102px 399px #FFF, 686px 1790px #FFF, 1083px 1164px #FFF, 112px 226px #FFF, 832px 1190px #FFF, 1612px 1866px #FFF, 1405px 1083px #FFF, 1983px 1434px #FFF, 1263px 1369px #FFF, 812px 764px #FFF, 514px 1569px #FFF, 510px 286px #FFF, 201px 1091px #FFF, 1972px 1959px #FFF, 1112px 1486px #FFF;
  animation: animStar 50s linear infinite;
}

.stars:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: 990px 1462px #FFF, 1859px 1557px #FFF, 666px 1823px #FFF, 622px 276px #FFF, 1421px 742px #FFF, 1118px 976px #FFF, 1569px 1407px #FFF, 794px 166px #FFF, 1782px 1475px #FFF, 1966px 1426px #FFF, 96px 1642px #FFF, 61px 376px #FFF, 770px 287px #FFF, 158px 356px #FFF, 228px 985px #FFF, 195px 1641px #FFF, 1731px 1517px #FFF, 436px 732px #FFF, 1184px 1458px #FFF, 1361px 558px #FFF, 1004px 947px #FFF, 1311px 1219px #FFF, 1568px 340px #FFF, 913px 1804px #FFF, 1952px 1924px #FFF, 531px 738px #FFF, 1846px 268px #FFF, 248px 184px #FFF, 86px 823px #FFF, 437px 1390px #FFF, 1660px 443px #FFF, 644px 702px #FFF, 1052px 1764px #FFF, 1353px 1122px #FFF, 1170px 425px #FFF, 434px 479px #FFF, 965px 765px #FFF, 667px 1749px #FFF, 1823px 1439px #FFF, 1287px 823px #FFF, 1998px 1068px #FFF, 409px 1087px #FFF, 1312px 1535px #FFF, 111px 519px #FFF, 490px 1497px #FFF, 186px 1544px #FFF, 1508px 1324px #FFF, 1184px 732px #FFF, 1150px 1759px #FFF, 25px 908px #FFF, 396px 161px #FFF, 51px 267px #FFF, 1160px 1256px #FFF, 1722px 8px #FFF, 334px 287px #FFF, 275px 902px #FFF, 1064px 806px #FFF, 140px 435px #FFF, 453px 1272px #FFF, 933px 1709px #FFF, 1446px 1563px #FFF, 182px 1721px #FFF, 1172px 1510px #FFF, 1744px 1086px #FFF, 233px 1233px #FFF, 137px 1430px #FFF, 297px 141px #FFF, 1654px 218px #FFF, 1111px 1701px #FFF, 333px 468px #FFF, 766px 1221px #FFF, 687px 649px #FFF, 555px 705px #FFF, 544px 660px #FFF, 1679px 815px #FFF, 448px 1697px #FFF, 704px 376px #FFF, 599px 822px #FFF, 1233px 1926px #FFF, 1439px 152px #FFF, 215px 1079px #FFF, 1096px 834px #FFF, 817px 594px #FFF, 160px 1486px #FFF, 248px 1442px #FFF, 1424px 895px #FFF, 733px 887px #FFF, 1100px 557px #FFF, 948px 569px #FFF, 1441px 1642px #FFF, 1651px 1547px #FFF, 601px 22px #FFF, 306px 11px #FFF, 612px 1615px #FFF, 1432px 1564px #FFF, 1073px 1683px #FFF, 1926px 137px #FFF, 786px 98px #FFF, 1346px 1262px #FFF, 574px 763px #FFF, 989px 1852px #FFF, 1188px 1660px #FFF, 726px 355px #FFF, 1574px 814px #FFF, 759px 1729px #FFF, 644px 286px #FFF, 373px 1272px #FFF, 837px 1106px #FFF, 1321px 819px #FFF, 1094px 1654px #FFF, 1194px 1674px #FFF, 363px 363px #FFF, 470px 1092px #FFF, 1254px 393px #FFF, 1727px 1958px #FFF, 1402px 682px #FFF, 379px 489px #FFF, 1815px 308px #FFF, 972px 890px #FFF, 1296px 1577px #FFF, 1678px 283px #FFF, 1760px 91px #FFF, 1764px 578px #FFF, 1319px 822px #FFF, 725px 1436px #FFF, 77px 1971px #FFF, 220px 1985px #FFF, 1151px 334px #FFF, 541px 279px #FFF, 39px 345px #FFF, 673px 643px #FFF, 777px 384px #FFF, 1876px 1788px #FFF, 810px 1329px #FFF, 57px 1829px #FFF, 1751px 1027px #FFF, 951px 467px #FFF, 1842px 1100px #FFF, 990px 1585px #FFF, 1907px 815px #FFF, 4px 1695px #FFF, 1439px 1075px #FFF, 1355px 1455px #FFF, 1279px 1706px #FFF, 986px 995px #FFF, 1082px 1913px #FFF, 897px 1031px #FFF, 1606px 549px #FFF, 758px 1608px #FFF, 340px 1221px #FFF, 1564px 1728px #FFF, 1097px 836px #FFF, 41px 762px #FFF, 1956px 377px #FFF, 1859px 157px #FFF, 309px 413px #FFF, 768px 675px #FFF, 693px 673px #FFF, 464px 467px #FFF, 1956px 683px #FFF, 1294px 1206px #FFF, 471px 1110px #FFF, 1877px 1816px #FFF, 1310px 404px #FFF, 489px 59px #FFF, 84px 540px #FFF, 78px 54px #FFF, 373px 1667px #FFF, 1317px 775px #FFF, 386px 361px #FFF, 667px 323px #FFF, 1987px 664px #FFF, 66px 303px #FFF, 1394px 846px #FFF, 1181px 1901px #FFF, 303px 1351px #FFF, 1630px 204px #FFF, 1199px 667px #FFF, 1003px 1924px #FFF, 839px 1583px #FFF, 103px 756px #FFF, 1561px 841px #FFF, 1166px 1136px #FFF, 899px 913px #FFF, 555px 465px #FFF, 1589px 93px #FFF, 546px 851px #FFF, 1603px 570px #FFF, 832px 1322px #FFF, 665px 1284px #FFF, 1092px 1237px #FFF, 626px 1893px #FFF, 1987px 983px #FFF, 865px 669px #FFF, 1586px 296px #FFF, 938px 415px #FFF, 1289px 1217px #FFF, 1079px 1904px #FFF, 1324px 1386px #FFF, 665px 1482px #FFF, 775px 622px #FFF, 1195px 530px #FFF, 1408px 699px #FFF, 1315px 1px #FFF, 1385px 1359px #FFF, 1328px 534px #FFF, 855px 7px #FFF, 1436px 25px #FFF, 1376px 199px #FFF, 39px 534px #FFF, 1136px 1635px #FFF, 909px 1584px #FFF, 895px 6px #FFF, 903px 308px #FFF, 1914px 1534px #FFF, 718px 1073px #FFF, 415px 53px #FFF, 1816px 119px #FFF, 1747px 1133px #FFF, 891px 352px #FFF, 186px 1626px #FFF, 1296px 1128px #FFF, 1328px 937px #FFF, 1486px 853px #FFF, 342px 346px #FFF, 284px 1156px #FFF, 376px 28px #FFF, 607px 1105px #FFF, 1325px 301px #FFF, 881px 1185px #FFF, 381px 1214px #FFF, 465px 254px #FFF, 1922px 1784px #FFF, 508px 1805px #FFF, 1403px 797px #FFF, 1024px 1918px #FFF, 1532px 1310px #FFF, 858px 810px #FFF, 208px 734px #FFF, 1543px 1848px #FFF, 1124px 344px #FFF, 546px 1467px #FFF, 1092px 307px #FFF, 282px 1054px #FFF, 903px 1625px #FFF, 279px 1698px #FFF, 1097px 1619px #FFF, 1438px 359px #FFF, 1266px 152px #FFF, 352px 1515px #FFF, 1855px 1781px #FFF, 293px 1924px #FFF, 999px 1688px #FFF, 387px 350px #FFF, 1220px 887px #FFF, 457px 510px #FFF, 1629px 587px #FFF, 832px 675px #FFF, 270px 9px #FFF, 771px 157px #FFF, 1559px 578px #FFF, 308px 1328px #FFF, 994px 1230px #FFF, 1059px 19px #FFF, 119px 367px #FFF, 386px 759px #FFF, 1731px 715px #FFF, 1798px 759px #FFF, 507px 559px #FFF, 1388px 1892px #FFF, 1972px 1655px #FFF, 675px 612px #FFF, 1615px 1431px #FFF, 1157px 1623px #FFF, 932px 229px #FFF, 1077px 632px #FFF, 1670px 330px #FFF, 1373px 879px #FFF, 1971px 363px #FFF, 1681px 1292px #FFF, 1015px 1318px #FFF, 755px 895px #FFF, 1945px 328px #FFF, 557px 1984px #FFF, 1755px 748px #FFF, 186px 175px #FFF, 780px 1830px #FFF, 396px 660px #FFF, 305px 192px #FFF, 1902px 628px #FFF, 658px 1463px #FFF, 1856px 1463px #FFF, 1628px 366px #FFF, 1793px 917px #FFF, 1129px 964px #FFF, 276px 386px #FFF, 1933px 1855px #FFF, 1257px 445px #FFF, 1145px 1434px #FFF, 1890px 130px #FFF, 1246px 1710px #FFF, 1544px 1119px #FFF, 556px 1422px #FFF, 1999px 1721px #FFF, 305px 1609px #FFF, 1124px 483px #FFF, 1278px 812px #FFF, 109px 1174px #FFF, 1690px 1726px #FFF, 1251px 1704px #FFF, 1130px 1270px #FFF, 1066px 1904px #FFF, 788px 831px #FFF, 789px 185px #FFF, 222px 286px #FFF, 660px 1593px #FFF, 1229px 873px #FFF, 1690px 299px #FFF, 965px 500px #FFF, 1376px 1061px #FFF, 378px 207px #FFF, 908px 1157px #FFF, 182px 232px #FFF, 43px 1660px #FFF, 1444px 1165px #FFF, 1526px 1381px #FFF, 944px 434px #FFF, 897px 1765px #FFF, 1784px 1335px #FFF, 1290px 1041px #FFF, 116px 1246px #FFF, 1082px 883px #FFF, 1816px 1636px #FFF, 1493px 1953px #FFF, 904px 566px #FFF, 621px 716px #FFF, 275px 1951px #FFF, 1310px 119px #FFF, 1378px 370px #FFF, 22px 902px #FFF, 565px 1340px #FFF, 1588px 1644px #FFF, 1313px 1933px #FFF, 550px 1049px #FFF, 1421px 313px #FFF, 1272px 1851px #FFF, 1359px 1859px #FFF, 1641px 1674px #FFF, 1546px 286px #FFF, 1946px 1570px #FFF, 491px 308px #FFF, 606px 380px #FFF, 361px 1317px #FFF, 1396px 477px #FFF, 1864px 1385px #FFF, 182px 1379px #FFF, 75px 33px #FFF, 990px 880px #FFF, 770px 111px #FFF, 1115px 1633px #FFF, 1527px 160px #FFF, 1867px 719px #FFF, 1046px 442px #FFF, 79px 47px #FFF, 143px 1764px #FFF, 896px 1182px #FFF, 1401px 585px #FFF, 1151px 783px #FFF, 476px 1906px #FFF, 589px 1741px #FFF, 306px 1731px #FFF, 779px 1670px #FFF, 144px 1362px #FFF, 1280px 819px #FFF, 328px 874px #FFF, 1420px 306px #FFF, 1445px 1660px #FFF, 1359px 860px #FFF, 181px 399px #FFF, 1488px 1989px #FFF, 964px 1927px #FFF, 164px 1302px #FFF, 378px 1642px #FFF, 1862px 552px #FFF, 1550px 1654px #FFF, 451px 915px #FFF, 243px 904px #FFF, 730px 1137px #FFF, 1793px 718px #FFF, 1026px 1272px #FFF, 1142px 1454px #FFF, 973px 1526px #FFF, 211px 1041px #FFF, 1938px 1731px #FFF, 39px 1077px #FFF, 80px 886px #FFF, 266px 1699px #FFF, 926px 648px #FFF, 1024px 596px #FFF, 1957px 1197px #FFF, 1870px 22px #FFF, 1031px 1835px #FFF, 1445px 70px #FFF, 1858px 1437px #FFF, 1240px 1176px #FFF, 993px 1068px #FFF, 334px 1315px #FFF, 1016px 182px #FFF, 594px 689px #FFF, 1347px 855px #FFF, 1054px 512px #FFF, 59px 1512px #FFF, 935px 1057px #FFF, 1606px 1483px #FFF, 1541px 809px #FFF, 951px 498px #FFF, 813px 265px #FFF, 1767px 628px #FFF, 69px 559px #FFF, 861px 1494px #FFF, 248px 1326px #FFF, 1763px 1762px #FFF, 1090px 1934px #FFF, 574px 350px #FFF, 254px 1789px #FFF, 664px 784px #FFF, 425px 1347px #FFF, 779px 724px #FFF, 1315px 756px #FFF, 163px 709px #FFF, 625px 379px #FFF, 765px 689px #FFF, 536px 1556px #FFF, 1000px 1633px #FFF, 1443px 417px #FFF, 208px 1490px #FFF, 13px 575px #FFF, 1534px 1001px #FFF, 1086px 450px #FFF, 190px 136px #FFF, 198px 1518px #FFF, 949px 714px #FFF, 567px 1140px #FFF, 1897px 979px #FFF, 299px 160px #FFF, 331px 234px #FFF, 1452px 1890px #FFF, 1165px 1564px #FFF, 599px 202px #FFF, 178px 1536px #FFF, 1101px 770px #FFF, 1700px 1336px #FFF, 1865px 416px #FFF, 1115px 1335px #FFF, 1814px 1876px #FFF, 62px 964px #FFF, 499px 302px #FFF, 1778px 1640px #FFF, 1954px 939px #FFF, 815px 1726px #FFF, 551px 823px #FFF, 980px 1670px #FFF, 1643px 220px #FFF, 1653px 1098px #FFF, 1081px 1178px #FFF, 1102px 1371px #FFF, 1113px 227px #FFF, 1132px 1926px #FFF, 1959px 307px #FFF, 1178px 1277px #FFF, 980px 13px #FFF, 565px 1196px #FFF, 557px 1133px #FFF, 106px 4px #FFF, 314px 1690px #FFF, 677px 1947px #FFF, 212px 853px #FFF, 1161px 602px #FFF, 481px 522px #FFF, 1112px 140px #FFF, 821px 446px #FFF, 249px 1779px #FFF, 1096px 869px #FFF, 1890px 634px #FFF, 826px 372px #FFF, 928px 1065px #FFF, 976px 414px #FFF, 1493px 1918px #FFF, 1299px 845px #FFF, 1988px 103px #FFF, 538px 1234px #FFF, 88px 559px #FFF, 1597px 1849px #FFF, 541px 271px #FFF, 1899px 859px #FFF, 775px 757px #FFF, 841px 500px #FFF, 482px 1776px #FFF, 753px 1377px #FFF, 1912px 1382px #FFF, 1858px 966px #FFF, 191px 1364px #FFF, 774px 1425px #FFF, 1917px 1101px #FFF, 740px 1554px #FFF, 1401px 619px #FFF, 263px 877px #FFF, 1108px 1483px #FFF, 1868px 1236px #FFF, 1490px 1595px #FFF, 1313px 735px #FFF, 299px 676px #FFF, 239px 1814px #FFF, 1741px 11px #FFF, 498px 731px #FFF, 337px 243px #FFF, 1798px 253px #FFF, 598px 479px #FFF, 1361px 1621px #FFF, 1925px 1499px #FFF, 521px 754px #FFF, 346px 1594px #FFF, 1337px 1893px #FFF, 814px 1903px #FFF, 1422px 622px #FFF, 1669px 958px #FFF, 913px 780px #FFF, 219px 518px #FFF, 1232px 1915px #FFF, 1560px 110px #FFF, 194px 234px #FFF, 94px 1462px #FFF, 503px 815px #FFF, 1546px 524px #FFF, 155px 1481px #FFF, 1719px 1118px #FFF, 1744px 1972px #FFF, 1445px 1382px #FFF, 1701px 488px #FFF, 1010px 551px #FFF, 1927px 613px #FFF, 1809px 1925px #FFF, 1757px 1804px #FFF, 1916px 537px #FFF, 1098px 1589px #FFF, 78px 1608px #FFF, 1333px 288px #FFF, 60px 1437px #FFF, 1003px 1111px #FFF, 1058px 1238px #FFF, 672px 1723px #FFF, 157px 619px #FFF, 448px 1755px #FFF, 502px 1143px #FFF, 34px 1732px #FFF, 1951px 657px #FFF, 244px 1012px #FFF, 1679px 658px #FFF, 603px 318px #FFF, 1435px 353px #FFF, 1147px 706px #FFF, 1318px 1887px #FFF, 1115px 292px #FFF, 538px 1843px #FFF, 709px 315px #FFF, 47px 987px #FFF, 42px 845px #FFF, 196px 3px #FFF, 1583px 1107px #FFF, 459px 137px #FFF, 45px 1458px #FFF, 314px 391px #FFF, 1433px 1142px #FFF, 1240px 901px #FFF, 716px 52px #FFF, 1550px 1576px #FFF, 495px 590px #FFF, 155px 1979px #FFF, 1647px 1226px #FFF, 576px 12px #FFF, 73px 240px #FFF, 1631px 1775px #FFF, 223px 960px #FFF, 1193px 696px #FFF, 1231px 1285px #FFF, 633px 735px #FFF, 419px 409px #FFF, 1803px 832px #FFF, 691px 194px #FFF, 1920px 1169px #FFF, 1299px 870px #FFF, 1859px 1582px #FFF, 617px 1021px #FFF, 236px 153px #FFF, 485px 1630px #FFF, 1394px 433px #FFF, 294px 1475px #FFF, 1759px 353px #FFF, 18px 760px #FFF, 1811px 1169px #FFF, 1836px 1673px #FFF, 1247px 1961px #FFF, 1231px 415px #FFF, 1138px 957px #FFF, 253px 928px #FFF, 1227px 1631px #FFF, 594px 1523px #FFF, 1295px 75px #FFF, 1277px 901px #FFF, 399px 1106px #FFF, 69px 62px #FFF, 146px 1366px #FFF, 1796px 706px #FFF, 1786px 56px #FFF, 1678px 108px #FFF, 746px 403px #FFF, 1892px 1497px #FFF, 1011px 1948px #FFF, 454px 1322px #FFF, 1765px 1506px #FFF, 373px 358px #FFF, 131px 1817px #FFF, 41px 259px #FFF, 63px 1250px #FFF, 1382px 1867px #FFF, 569px 1870px #FFF, 763px 388px #FFF, 149px 114px #FFF, 1490px 736px #FFF, 817px 1258px #FFF, 1833px 717px #FFF, 980px 763px #FFF, 823px 483px #FFF, 1522px 1401px #FFF, 1px 1674px #FFF, 1752px 1897px #FFF, 1813px 1770px #FFF, 135px 1735px #FFF, 277px 569px #FFF, 1410px 459px #FFF, 600px 386px #FFF, 1104px 1708px #FFF, 1081px 661px #FFF, 645px 715px #FFF, 220px 1948px #FFF, 1976px 39px #FFF, 1541px 1945px #FFF, 745px 207px #FFF, 1333px 1096px #FFF, 18px 1285px #FFF, 1134px 504px #FFF, 511px 1234px #FFF, 708px 1908px #FFF, 1121px 571px #FFF, 1686px 1872px #FFF, 382px 214px #FFF, 923px 1699px #FFF, 1471px 533px #FFF, 991px 268px #FFF, 81px 1152px #FFF, 1922px 180px #FFF, 90px 1341px #FFF, 974px 1025px #FFF, 811px 401px #FFF, 1200px 1573px #FFF, 197px 285px #FFF, 349px 1838px #FFF, 523px 647px #FFF, 1562px 1535px #FFF, 1243px 498px #FFF, 268px 1536px #FFF, 775px 1772px #FFF, 751px 157px #FFF, 524px 710px #FFF, 373px 971px #FFF, 290px 1377px #FFF, 1121px 128px #FFF, 323px 1678px #FFF, 897px 473px #FFF, 675px 1581px #FFF, 1279px 1605px #FFF, 1161px 316px #FFF, 1647px 1055px #FFF, 1480px 176px #FFF, 664px 621px #FFF, 102px 399px #FFF, 686px 1790px #FFF, 1083px 1164px #FFF, 112px 226px #FFF, 832px 1190px #FFF, 1612px 1866px #FFF, 1405px 1083px #FFF, 1983px 1434px #FFF, 1263px 1369px #FFF, 812px 764px #FFF, 514px 1569px #FFF, 510px 286px #FFF, 201px 1091px #FFF, 1972px 1959px #FFF, 1112px 1486px #FFF;
}

.stars2 {
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: 1749px 585px #FFF, 1334px 744px #FFF, 253px 244px #FFF, 896px 78px #FFF, 905px 1336px #FFF, 732px 800px #FFF, 1780px 1878px #FFF, 947px 1447px #FFF, 1390px 1464px #FFF, 981px 410px #FFF, 1753px 770px #FFF, 1587px 236px #FFF, 662px 230px #FFF, 658px 1427px #FFF, 637px 1616px #FFF, 636px 290px #FFF, 720px 58px #FFF, 468px 773px #FFF, 135px 1070px #FFF, 1880px 166px #FFF, 947px 1123px #FFF, 365px 197px #FFF, 415px 776px #FFF, 1002px 1287px #FFF, 1548px 658px #FFF, 1619px 523px #FFF, 653px 1661px #FFF, 439px 175px #FFF, 1623px 1673px #FFF, 238px 1000px #FFF, 846px 1702px #FFF, 1323px 1459px #FFF, 597px 286px #FFF, 610px 115px #FFF, 758px 38px #FFF, 1211px 1306px #FFF, 1117px 1088px #FFF, 1870px 950px #FFF, 1447px 324px #FFF, 481px 1196px #FFF, 1317px 180px #FFF, 1801px 784px #FFF, 53px 85px #FFF, 1230px 1992px #FFF, 847px 1643px #FFF, 764px 391px #FFF, 1754px 1812px #FFF, 1451px 151px #FFF, 532px 733px #FFF, 1998px 386px #FFF, 1467px 976px #FFF, 329px 1889px #FFF, 1476px 1265px #FFF, 1549px 1907px #FFF, 1002px 1950px #FFF, 1811px 1555px #FFF, 783px 1903px #FFF, 731px 1292px #FFF, 304px 351px #FFF, 1454px 960px #FFF, 1112px 939px #FFF, 1958px 1461px #FFF, 1157px 1906px #FFF, 1541px 1275px #FFF, 1755px 603px #FFF, 1587px 1784px #FFF, 284px 1643px #FFF, 232px 30px #FFF, 1462px 608px #FFF, 38px 1915px #FFF, 1920px 1358px #FFF, 566px 1596px #FFF, 1055px 1760px #FFF, 1378px 893px #FFF, 1270px 434px #FFF, 1738px 1271px #FFF, 1212px 97px #FFF, 766px 1006px #FFF, 585px 694px #FFF, 1267px 584px #FFF, 571px 1804px #FFF, 452px 1688px #FFF, 1442px 709px #FFF, 1733px 1479px #FFF, 1592px 1081px #FFF, 934px 1259px #FFF, 663px 454px #FFF, 141px 101px #FFF, 1823px 335px #FFF, 329px 1016px #FFF, 1905px 1382px #FFF, 1447px 1032px #FFF, 1508px 1386px #FFF, 849px 122px #FFF, 858px 650px #FFF, 1548px 551px #FFF, 363px 1778px #FFF, 1166px 742px #FFF, 785px 826px #FFF, 1650px 1327px #FFF, 306px 496px #FFF, 363px 367px #FFF, 1282px 1886px #FFF, 1278px 1772px #FFF, 771px 1252px #FFF, 1000px 309px #FFF, 1578px 228px #FFF, 317px 1748px #FFF, 1622px 1736px #FFF, 1771px 1527px #FFF, 1555px 938px #FFF, 1650px 1895px #FFF, 782px 1484px #FFF, 462px 231px #FFF, 1315px 1314px #FFF, 1233px 376px #FFF, 821px 1638px #FFF, 974px 1220px #FFF, 1781px 1802px #FFF, 311px 301px #FFF, 1930px 563px #FFF, 708px 1959px #FFF, 1585px 1660px #FFF, 1907px 225px #FFF, 23px 27px #FFF, 406px 1951px #FFF, 94px 1545px #FFF, 431px 1798px #FFF, 940px 1504px #FFF, 1215px 1448px #FFF, 1559px 213px #FFF, 1444px 1826px #FFF, 480px 1155px #FFF, 1666px 683px #FFF, 1826px 89px #FFF, 1086px 1627px #FFF, 1951px 582px #FFF, 109px 1699px #FFF, 181px 937px #FFF, 1966px 1811px #FFF, 791px 387px #FFF, 1523px 343px #FFF, 683px 65px #FFF, 1851px 321px #FFF, 1169px 82px #FFF, 580px 1077px #FFF, 68px 1254px #FFF, 410px 723px #FFF, 1232px 1424px #FFF, 400px 438px #FFF, 1804px 1901px #FFF, 353px 638px #FFF, 266px 661px #FFF, 923px 899px #FFF, 1692px 1991px #FFF, 211px 504px #FFF, 388px 357px #FFF, 1582px 866px #FFF, 727px 606px #FFF, 225px 479px #FFF, 138px 1086px #FFF, 1167px 1932px #FFF, 1921px 596px #FFF, 1881px 747px #FFF, 832px 1491px #FFF, 1179px 1097px #FFF, 1188px 831px #FFF, 443px 230px #FFF, 1219px 1560px #FFF, 1229px 769px #FFF, 1544px 953px #FFF, 1299px 1539px #FFF, 831px 848px #FFF, 1771px 1355px #FFF, 364px 959px #FFF, 41px 1387px #FFF, 801px 549px #FFF, 1492px 1676px #FFF, 180px 1649px #FFF, 943px 559px #FFF, 176px 1094px #FFF, 1354px 630px #FFF, 1877px 1865px #FFF, 550px 527px #FFF, 1641px 230px #FFF, 1827px 1002px #FFF, 1800px 1391px #FFF, 58px 1360px #FFF, 1472px 450px #FFF, 1538px 1580px #FFF, 48px 824px #FFF, 1156px 829px #FFF, 571px 95px #FFF, 561px 1830px #FFF, 1711px 1216px #FFF, 1412px 1036px #FFF, 1925px 886px #FFF, 938px 113px #FFF, 1215px 472px #FFF, 1233px 1921px #FFF;
  animation: animStar 100s linear infinite;
}

.stars2:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: 1749px 585px #FFF, 1334px 744px #FFF, 253px 244px #FFF, 896px 78px #FFF, 905px 1336px #FFF, 732px 800px #FFF, 1780px 1878px #FFF, 947px 1447px #FFF, 1390px 1464px #FFF, 981px 410px #FFF, 1753px 770px #FFF, 1587px 236px #FFF, 662px 230px #FFF, 658px 1427px #FFF, 637px 1616px #FFF, 636px 290px #FFF, 720px 58px #FFF, 468px 773px #FFF, 135px 1070px #FFF, 1880px 166px #FFF, 947px 1123px #FFF, 365px 197px #FFF, 415px 776px #FFF, 1002px 1287px #FFF, 1548px 658px #FFF, 1619px 523px #FFF, 653px 1661px #FFF, 439px 175px #FFF, 1623px 1673px #FFF, 238px 1000px #FFF, 846px 1702px #FFF, 1323px 1459px #FFF, 597px 286px #FFF, 610px 115px #FFF, 758px 38px #FFF, 1211px 1306px #FFF, 1117px 1088px #FFF, 1870px 950px #FFF, 1447px 324px #FFF, 481px 1196px #FFF, 1317px 180px #FFF, 1801px 784px #FFF, 53px 85px #FFF, 1230px 1992px #FFF, 847px 1643px #FFF, 764px 391px #FFF, 1754px 1812px #FFF, 1451px 151px #FFF, 532px 733px #FFF, 1998px 386px #FFF, 1467px 976px #FFF, 329px 1889px #FFF, 1476px 1265px #FFF, 1549px 1907px #FFF, 1002px 1950px #FFF, 1811px 1555px #FFF, 783px 1903px #FFF, 731px 1292px #FFF, 304px 351px #FFF, 1454px 960px #FFF, 1112px 939px #FFF, 1958px 1461px #FFF, 1157px 1906px #FFF, 1541px 1275px #FFF, 1755px 603px #FFF, 1587px 1784px #FFF, 284px 1643px #FFF, 232px 30px #FFF, 1462px 608px #FFF, 38px 1915px #FFF, 1920px 1358px #FFF, 566px 1596px #FFF, 1055px 1760px #FFF, 1378px 893px #FFF, 1270px 434px #FFF, 1738px 1271px #FFF, 1212px 97px #FFF, 766px 1006px #FFF, 585px 694px #FFF, 1267px 584px #FFF, 571px 1804px #FFF, 452px 1688px #FFF, 1442px 709px #FFF, 1733px 1479px #FFF, 1592px 1081px #FFF, 934px 1259px #FFF, 663px 454px #FFF, 141px 101px #FFF, 1823px 335px #FFF, 329px 1016px #FFF, 1905px 1382px #FFF, 1447px 1032px #FFF, 1508px 1386px #FFF, 849px 122px #FFF, 858px 650px #FFF, 1548px 551px #FFF, 363px 1778px #FFF, 1166px 742px #FFF, 785px 826px #FFF, 1650px 1327px #FFF, 306px 496px #FFF, 363px 367px #FFF, 1282px 1886px #FFF, 1278px 1772px #FFF, 771px 1252px #FFF, 1000px 309px #FFF, 1578px 228px #FFF, 317px 1748px #FFF, 1622px 1736px #FFF, 1771px 1527px #FFF, 1555px 938px #FFF, 1650px 1895px #FFF, 782px 1484px #FFF, 462px 231px #FFF, 1315px 1314px #FFF, 1233px 376px #FFF, 821px 1638px #FFF, 974px 1220px #FFF, 1781px 1802px #FFF, 311px 301px #FFF, 1930px 563px #FFF, 708px 1959px #FFF, 1585px 1660px #FFF, 1907px 225px #FFF, 23px 27px #FFF, 406px 1951px #FFF, 94px 1545px #FFF, 431px 1798px #FFF, 940px 1504px #FFF, 1215px 1448px #FFF, 1559px 213px #FFF, 1444px 1826px #FFF, 480px 1155px #FFF, 1666px 683px #FFF, 1826px 89px #FFF, 1086px 1627px #FFF, 1951px 582px #FFF, 109px 1699px #FFF, 181px 937px #FFF, 1966px 1811px #FFF, 791px 387px #FFF, 1523px 343px #FFF, 683px 65px #FFF, 1851px 321px #FFF, 1169px 82px #FFF, 580px 1077px #FFF, 68px 1254px #FFF, 410px 723px #FFF, 1232px 1424px #FFF, 400px 438px #FFF, 1804px 1901px #FFF, 353px 638px #FFF, 266px 661px #FFF, 923px 899px #FFF, 1692px 1991px #FFF, 211px 504px #FFF, 388px 357px #FFF, 1582px 866px #FFF, 727px 606px #FFF, 225px 479px #FFF, 138px 1086px #FFF, 1167px 1932px #FFF, 1921px 596px #FFF, 1881px 747px #FFF, 832px 1491px #FFF, 1179px 1097px #FFF, 1188px 831px #FFF, 443px 230px #FFF, 1219px 1560px #FFF, 1229px 769px #FFF, 1544px 953px #FFF, 1299px 1539px #FFF, 831px 848px #FFF, 1771px 1355px #FFF, 364px 959px #FFF, 41px 1387px #FFF, 801px 549px #FFF, 1492px 1676px #FFF, 180px 1649px #FFF, 943px 559px #FFF, 176px 1094px #FFF, 1354px 630px #FFF, 1877px 1865px #FFF, 550px 527px #FFF, 1641px 230px #FFF, 1827px 1002px #FFF, 1800px 1391px #FFF, 58px 1360px #FFF, 1472px 450px #FFF, 1538px 1580px #FFF, 48px 824px #FFF, 1156px 829px #FFF, 571px 95px #FFF, 561px 1830px #FFF, 1711px 1216px #FFF, 1412px 1036px #FFF, 1925px 886px #FFF, 938px 113px #FFF, 1215px 472px #FFF, 1233px 1921px #FFF;
}

.stars3 {
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: 1933px 598px #FFF, 1261px 1783px #FFF, 1101px 1607px #FFF, 500px 17px #FFF, 587px 1613px #FFF, 1635px 689px #FFF, 520px 56px #FFF, 1829px 202px #FFF, 464px 1752px #FFF, 872px 1381px #FFF, 1981px 1264px #FFF, 745px 208px #FFF, 1564px 1400px #FFF, 1959px 826px #FFF, 1567px 1722px #FFF, 1180px 419px #FFF, 835px 1420px #FFF, 1298px 928px #FFF, 11px 11px #FFF, 719px 1373px #FFF, 1547px 1676px #FFF, 1261px 145px #FFF, 36px 608px #FFF, 1843px 1291px #FFF, 1943px 508px #FFF, 1346px 241px #FFF, 589px 1740px #FFF, 646px 475px #FFF, 1608px 592px #FFF, 98px 1053px #FFF, 602px 930px #FFF, 1397px 1309px #FFF, 610px 38px #FFF, 125px 1861px #FFF, 812px 495px #FFF, 1548px 1133px #FFF, 346px 1683px #FFF, 1434px 350px #FFF, 117px 274px #FFF, 1500px 658px #FFF, 324px 563px #FFF, 906px 720px #FFF, 1264px 224px #FFF, 1565px 501px #FFF, 1871px 1752px #FFF, 292px 1986px #FFF, 839px 949px #FFF, 652px 788px #FFF, 1114px 1302px #FFF, 1913px 1900px #FFF, 1493px 526px #FFF, 677px 1461px #FFF, 931px 1755px #FFF, 696px 571px #FFF, 1874px 1920px #FFF, 414px 1692px #FFF, 596px 215px #FFF, 1468px 671px #FFF, 1865px 234px #FFF, 893px 668px #FFF, 1209px 49px #FFF, 1351px 761px #FFF, 1368px 969px #FFF, 544px 461px #FFF, 1873px 1603px #FFF, 99px 1382px #FFF, 1945px 706px #FFF, 932px 616px #FFF, 1864px 1301px #FFF, 563px 737px #FFF, 977px 1844px #FFF, 950px 1765px #FFF, 118px 1293px #FFF, 1139px 840px #FFF, 138px 586px #FFF, 398px 1046px #FFF, 1874px 1221px #FFF, 952px 190px #FFF, 688px 182px #FFF, 1895px 1126px #FFF, 526px 521px #FFF, 1789px 162px #FFF, 642px 110px #FFF, 534px 964px #FFF, 1914px 154px #FFF, 1630px 1419px #FFF, 289px 630px #FFF, 1349px 1804px #FFF, 1489px 1412px #FFF, 571px 122px #FFF, 911px 1741px #FFF, 1247px 990px #FFF, 344px 483px #FFF, 778px 1909px #FFF, 1910px 380px #FFF, 153px 549px #FFF, 1104px 1961px #FFF, 1979px 1032px #FFF, 398px 187px #FFF, 60px 81px #FFF;
  animation: animStar 150s linear infinite;
}

.stars3:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: 1933px 598px #FFF, 1261px 1783px #FFF, 1101px 1607px #FFF, 500px 17px #FFF, 587px 1613px #FFF, 1635px 689px #FFF, 520px 56px #FFF, 1829px 202px #FFF, 464px 1752px #FFF, 872px 1381px #FFF, 1981px 1264px #FFF, 745px 208px #FFF, 1564px 1400px #FFF, 1959px 826px #FFF, 1567px 1722px #FFF, 1180px 419px #FFF, 835px 1420px #FFF, 1298px 928px #FFF, 11px 11px #FFF, 719px 1373px #FFF, 1547px 1676px #FFF, 1261px 145px #FFF, 36px 608px #FFF, 1843px 1291px #FFF, 1943px 508px #FFF, 1346px 241px #FFF, 589px 1740px #FFF, 646px 475px #FFF, 1608px 592px #FFF, 98px 1053px #FFF, 602px 930px #FFF, 1397px 1309px #FFF, 610px 38px #FFF, 125px 1861px #FFF, 812px 495px #FFF, 1548px 1133px #FFF, 346px 1683px #FFF, 1434px 350px #FFF, 117px 274px #FFF, 1500px 658px #FFF, 324px 563px #FFF, 906px 720px #FFF, 1264px 224px #FFF, 1565px 501px #FFF, 1871px 1752px #FFF, 292px 1986px #FFF, 839px 949px #FFF, 652px 788px #FFF, 1114px 1302px #FFF, 1913px 1900px #FFF, 1493px 526px #FFF, 677px 1461px #FFF, 931px 1755px #FFF, 696px 571px #FFF, 1874px 1920px #FFF, 414px 1692px #FFF, 596px 215px #FFF, 1468px 671px #FFF, 1865px 234px #FFF, 893px 668px #FFF, 1209px 49px #FFF, 1351px 761px #FFF, 1368px 969px #FFF, 544px 461px #FFF, 1873px 1603px #FFF, 99px 1382px #FFF, 1945px 706px #FFF, 932px 616px #FFF, 1864px 1301px #FFF, 563px 737px #FFF, 977px 1844px #FFF, 950px 1765px #FFF, 118px 1293px #FFF, 1139px 840px #FFF, 138px 586px #FFF, 398px 1046px #FFF, 1874px 1221px #FFF, 952px 190px #FFF, 688px 182px #FFF, 1895px 1126px #FFF, 526px 521px #FFF, 1789px 162px #FFF, 642px 110px #FFF, 534px 964px #FFF, 1914px 154px #FFF, 1630px 1419px #FFF, 289px 630px #FFF, 1349px 1804px #FFF, 1489px 1412px #FFF, 571px 122px #FFF, 911px 1741px #FFF, 1247px 990px #FFF, 344px 483px #FFF, 778px 1909px #FFF, 1910px 380px #FFF, 153px 549px #FFF, 1104px 1961px #FFF, 1979px 1032px #FFF, 398px 187px #FFF, 60px 81px #FFF;
}

@keyframes animStar {
  from {
    transform: translateY(0px);
  }

  to {
    transform: translateY(-2000px);
  }
}
</style>