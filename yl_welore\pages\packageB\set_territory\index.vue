<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <view class="bg-ghostWhite" style="padding: 30rpx; min-height: 100vh;">
            <!-- 名称输入区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">设置名称</text>
                        </view>
                        <input
                            @input="get_form_value"
                            data-type="1"
                            name="qq_name"
                            maxlength="14"
                            style="padding: 20rpx; height: 80rpx; border-radius: 12rpx; border: 2rpx solid #e1e1e1; font-size: 32rpx; font-weight: 500; color: #333; background: #fafafa;"
                            placeholder="请输入名称"
                        />
                    </view>
                </view>
            </view>

            <!-- 规则说明区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">命名规则</text>
                        </view>
                        <view class="text-gray" style="padding: 10rpx 0; font-size: 28rpx; line-height: 1.6;">
                            <view style="margin-bottom: 10rpx;">📝 1. 名称不超过14个汉字，限汉字、字母、数字和下划线</view>
                            <view style="margin-bottom: 10rpx;">🔄 2. 名称不能与已有名称重复</view>
                            <view>⚠️ 3. 名称不能包含"医疗机构、具有药用性产品名、股票期货彩票"等金融信息</view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 类型选择区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">类型选择</text>
                        </view>
                        <picker @change="needleChange" :value="needle_index" range-key="name" :range="needle">
                            <view class="picker flex align-center justify-between" style="padding: 20rpx; background: #fafafa; border-radius: 12rpx; border: 2rpx solid #e1e1e1;">
                                <text style="font-size: 32rpx; color: #333;">{{ needle[needle_index] ? needle[needle_index].name : '请选择类型' }}</text>
                                <text class="cuIcon-right text-gray" style="font-size: 28rpx;"></text>
                            </view>
                        </picker>
                    </view>
                </view>
            </view>

            <!-- 简介输入区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">简介描述</text>
                        </view>
                        <textarea
                            @input="get_form_value"
                            data-type="2"
                            name="qq_content"
                            style="height: 200rpx; padding: 20rpx; width: 100%; border: 2rpx solid #e1e1e1; border-radius: 12rpx; background: #fafafa; font-size: 32rpx; color: #333;"
                            value=""
                            class="weui-textarea"
                            maxlength="140"
                            placeholder="请填写简介描述，不超过140个字"
                        />
                    </view>
                </view>
            </view>
            <!-- 头像上传区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">头像设置</text>
                        </view>
                        <view class="flex align-center justify-between" style="padding: 20rpx; background: #fafafa; border-radius: 12rpx; border: 2rpx solid #e1e1e1;">
                            <view v-if="!img_botton" class="flex align-center">
                                <view style="position: relative; margin-right: 30rpx;">
                                    <image
                                        class="radius"
                                        :data-src="img_arr"
                                        @tap="previewImage"
                                        style="width: 120rpx; height: 120rpx; border-radius: 12rpx;"
                                        :src="img_arr"
                                        mode="aspectFill"
                                    />
                                    <text
                                        style="position: absolute; top: -10rpx; right: -10rpx; font-size: 40rpx;"
                                        :data-index="vido_index"
                                        @tap="clearOneImage"
                                        class="cuIcon-roundclosefill text-red"
                                    ></text>
                                </view>
                                <view>
                                    <text style="font-size: 28rpx; color: #666;">📸 头像已上传</text>
                                    <view style="font-size: 24rpx; color: #999; margin-top: 10rpx;">点击图片可预览</view>
                                </view>
                            </view>
                            <view v-if="img_botton" class="flex align-center" @tap="previewOneImage">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/applet_icon/bpj.png'"
                                    style="width: 120rpx; height: 120rpx; border-radius: 12rpx; margin-right: 30rpx;"
                                ></image>
                                <view>
                                    <text style="font-size: 28rpx; color: #666;">点击上传头像</text>
                                    <view style="font-size: 24rpx; color: #999; margin-top: 10rpx;">建议正方形图片</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 申请原因区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 20rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">💬 申请原因</text>
                        </view>
                        <textarea
                            @input="get_form_value"
                            data-type="3"
                            name="qq_apply"
                            style="height: 200rpx; padding: 20rpx; width: 100%; border: 2rpx solid #e1e1e1; border-radius: 12rpx; background: #fafafa; font-size: 32rpx; color: #333;"
                            value=""
                            class="weui-textarea"
                            maxlength="140"
                            placeholder="请填写申请原因，不超过140个字"
                        />
                    </view>
                </view>
            </view>

            <!-- 权限设置区域 -->
            <view class="cu-card case no-card" style="margin-bottom: 20rpx;">
                <view class="cu-item shadow">
                    <view class="content" style="padding: 30rpx;">
                        <view class="flex align-center" style="margin-bottom: 30rpx;">
                            <text style="font-size: 32rpx; font-weight: 600; color: #333;">权限设置</text>
                        </view>

                        <view class="cu-form-group" style="border: none; padding: 20rpx; background: #fafafa; border-radius: 12rpx; margin-bottom: 20rpx;">
                            <view class="title flex align-center">
                                <text>👑 是否申请{{ design.qq_name }}主</text>
                            </view>
                            <switch @change="onChange"></switch>
                        </view>

                        <view class="cu-form-group" style="border: none; padding: 20rpx; background: #fafafa; border-radius: 12rpx;">
                            <view class="title flex align-center">
                                <text>🔒 是否开启{{ design.landgrave }}权限</text>
                            </view>
                            <switch class="red sm" @change="onChange_qx"></switch>
                        </view>

                        <view class="text-gray" style="padding: 20rpx; background: #f0f9ff; border-radius: 12rpx; margin-top: 20rpx; font-size: 26rpx; line-height: 1.5;">
                            💡 说明：只有加入{{ design.landgrave }}的人才可以看到{{ design.landgrave }}里发布的内容，加入{{ design.landgrave }}，需要通过{{ design.landgrave }}管理员审核
                        </view>
                    </view>
                </view>
            </view>

            <!-- 提交按钮 -->
            <view style="padding: 0 30rpx; margin-top: 40rpx;">
                <button
                    class="cu-btn round block shadow"
                    :disabled="is_submit"
                    style="background: linear-gradient(45deg, #39b54a, #8dc63f); color: white; height: 90rpx; font-size: 32rpx; font-weight: 600;"
                    @tap="formSubmit"
                >
                    提交申请
                </button>
            </view>
        </view>

        <view class="cu-load load-modal" v-if="loadModal">
            <view class="gray-text">上传中...</view>
        </view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            needle: [],
            needle_index: 0,
            img_arr: '',
            //图片
            img_botton: true,
            this_qz: false,
            this_qx: false,
            is_submit: false,
            title: '',
            //导航栏 中间的标题
            qq_name: '',
            qq_content: '',
            qq_apply: '',
            design: {},
            loadModal: false,
            qz_model: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        const design = uni.getStorageSync('is_diy');
        this.design = design;
        this.title = '创建' + design['landgrave'];
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        uni.hideShareMenu({
            menus: ['shareAppMessage', 'shareTimeline']
        });
        this.get_needle();
        const subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        const forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        onChange(event) {
            console.log(event);
            const detail = event.detail;
            if (!detail.value) {
                this.this_qz = detail.value;
            } else {
                this.this_qz = detail.value;
                this.qz_model = true;
            }
        },
        onChange_qx(event) {
            console.log(event);
            const detail = event.detail;
            this.this_qx = detail.value;
        },
        get_form_value(dd) {
            const value = dd.detail.value;
            const type = dd.target.dataset.type;
            if (type == 1) {
                //圈子名称
                this.qq_name = value;
                return;
            }
            if (type == 2) {
                //简介
                this.qq_content = value;
                return;
            }
            if (type == 3) {
                //原因
                this.qq_apply = value;
                return;
            }
        },
        /**
         * 提交申请
         */
        formSubmit(d) {
            this.is_submit = true;
            const params = new Object();
            const e = app.globalData.getCache('userinfo');
            params.needle_id = this.needle[this.needle_index]['id'];
            params.realm_icon = this.img_arr;
            params.is_gnaw_qulord = this.this_qz == false ? 0 : 1;
            params.attention = this.this_qx == false ? 0 : 1;
            params.realm_name = this.qq_name;
            params.realm_synopsis = this.qq_content;
            params.solicit_origin = this.qq_apply;
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            const b = app.globalData.api_root + 'User/add_territory_petition';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        const subscribe = app.globalData.getCache('subscribe');
                        if (subscribe && subscribe['YL0009'] && subscribe['YL0008'] && subscribe['YL0010']) {
                            app.globalData.authorization(subscribe['YL0009'], subscribe['YL0008'], subscribe['YL0010'], (res) => {
                                setTimeout(function () {
                                    uni.navigateBack();
                                }, 2000);
                            });
                        } else {
                            setTimeout(function () {
                                uni.navigateBack();
                            }, 2000);
                        }
                    } else {
                        this.is_submit = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 选择类型
         */
        needleChange(e) {
            this.needle_index = e.detail.value;
        },
        /**
         * 获取大分类
         */
        get_needle() {
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            const b = app.globalData.api_root + 'User/get_left_needle';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.needle = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 上传主图
         */
        previewOneImage() {
            const e = app.globalData.getCache('userinfo');
            const b = app.globalData.api_root + 'User/img_upload';
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                success: (res) => {
                    this.loadModal = true;
                    const tempFilePaths = res.tempFilePaths;
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths[0],
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            much_id: app.globalData.siteInfo.uniacid
                        },
                        success: (res) => {
                            console.log(res);
                            const data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                uni.showToast({
                                    title: data.msg,
                                    icon: 'none',
                                    duration: 2000
                                });
                            } else {
                                this.img_arr = data.url;
                                this.img_botton = false;
                            }
                            console.log(this.img_botton);
                            this.loadModal = false;
                        },
                        fail: (res) => {
                            uni.showToast({
                                title: '上传错误！',
                                icon: 'error',
                                duration: 2000
                            });
                        }
                    });
                }
            });
        },
        /**
         * 删除图片
         */
        clearOneImage(e) {
            this.img_arr = '';
            this.img_botton = true;
        },
        _navback() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
.nav-wrap {
    width: 100%;
    top: 0;
    background: #fff;
    color: #000;
    z-index: 9999999;
}
.i-modal-actions {
    height: 10px !important;
}
.zan_style_test {
    float: right;
    margin-top: 26rpx;
    margin-right: 20rpx;
    font-size: 12px;
}
.zan_style {
    float: right;
    margin-top: 17rpx;
    margin-right: 4rpx;
}

.weui-tabbar {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: fixed;
    z-index: 500;
    bottom: 0;
    width: 100%;
    background-color: #f7f7fa;
}

/* 标题要居中 */

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    font-size: 36rpx;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 55rpx;
    justify-content: space-around;
    border-radius: 50rpx;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 40rpx;
    height: 40rpx;
    margin-top: 10rpx;
    margin-left: -2rpx;
}
.liwu_col {
    box-sizing: border-box;
    text-align: center;
    margin-top: 30rpx;
    display: inline-block;
    width: 25%;
    height: 7.5em;
    border-radius: 10px;
    border: 1px solid #f1f1f1;
    margin-right: 10px;
    margin-left: 10px;
    position: relative;
}
.select {
    border: 1px solid #f3cb5a;
    background-color: #fdf3da;
}

.user_col {
    box-sizing: border-box;
    text-align: center;
    display: inline-block;
    width: 33%;
    height: 7.5em;
    border-radius: 10px;
}
.add_submit {
    background: #ffffff;
    color: #666666;
    border-radius: 2rem;
    border: 1px #666666 solid;
    box-shadow: -3px 5px 10rpx 5rpx#666666;
}
</style>
